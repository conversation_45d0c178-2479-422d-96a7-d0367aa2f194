<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Support\Facades\URL;

class ResultControllerTest extends TestCase
{

    public function test_result_routes_exist()
    {
        // Test that the routes are properly registered
        $this->assertTrue(
            collect(\Illuminate\Support\Facades\Route::getRoutes())
                ->contains(fn($route) => $route->getName() === 'result.print')
        );

        $this->assertTrue(
            collect(\Illuminate\Support\Facades\Route::getRoutes())
                ->contains(fn($route) => $route->getName() === 'result.download')
        );
    }

    public function test_result_controller_exists()
    {
        // Test that the ResultController class exists
        $this->assertTrue(class_exists(\App\Http\Controllers\ResultController::class));
    }

    public function test_result_document_template_exists()
    {
        // Test that the result document template exists
        $this->assertTrue(view()->exists('filament.documents.result'));
    }

    public function test_result_component_template_exists()
    {
        // Test that the result component template exists
        $this->assertTrue(view()->exists('components.result'));
    }

    public function test_signed_routes_can_be_generated()
    {
        // Test that signed routes can be generated with a dummy cache key
        $printUrl = URL::signedRoute('result.print', ['resultCacheKey' => 'test_cache_key']);
        $downloadUrl = URL::signedRoute('result.download', ['resultCacheKey' => 'test_cache_key']);

        $this->assertNotEmpty($printUrl);
        $this->assertNotEmpty($downloadUrl);
        $this->assertStringContainsString('result/print', $printUrl);
        $this->assertStringContainsString('result/download', $downloadUrl);
    }
}
