<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ asset('images/racoed-favicon.png') }}" type="image/png">
    <title>{{ $fileName }}</title>
    @livewireStyles
    @filamentStyles
    @vite(['resources/css/filament/custom/theme.css', 'resources/css/app.css'])
    <style>
        @media print {
            @page {
                size: A4;
                margin: 0mm;
            }

            .college-name-print {
                font-size: 20pt !important;
            }

            .print\:hidden {
                display: none !important;
            }

            body {
                font-size: 10px;
            }

            h1 {
                font-size: 16px !important;
            }

            h2 {
                font-size: 14px !important;
            }

            h3 {
                font-size: 12px !important;
            }

            th,
            td {
                font-size: 10px !important;
            }

            .print-grid {
                display: grid !important;
                grid-template-columns: 1fr 1fr !important;
                gap: 1rem;
            }

            th,
            tfoot tr {
                background-color: #f9fafb !important;
                /* <PERSON><PERSON><PERSON>'s gray-50 */
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            * {
                border-color: #6b7280 !important;
                /* gray-500 */
                border-radius: 2px !important;
                /* small radius */
            }
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-4xl mx-auto p-4 sm:p-2 text-sm space-y-3">
        {{-- HEADER --}}
        <x-document-header :collegeLogo="asset('images/racoed-favicon.png')" :collegeName="$collegeSettings->name"
            :collegeMotto="$collegeSettings->motto" :collegeAddress="$collegeSettings->address"
            :collegePhone="$collegeSettings->phone" :collegeEmail="$collegeSettings->email"
            :studentPhoto="$student->photo ? Storage::url($student->photo) : asset('images/placeholder.png')"
            heading="Examination & Records" subheading="Student Result Sheet" />

        {{-- STUDENT & ACADEMIC DETAILS --}}
        <div class="border p-1">
            <h2 class="text-center font-bold mb-2">Student & Academic Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-1">
                <div class="border px-2 py-0.5"><strong>Name:</strong> {{ $student->name ?? 'NIL' }}</div>
                <div class="border px-2 py-0.5"><strong>Session:</strong> {{ $session ?? 'NIL' }}</div>
                <div class="border px-2 py-0.5"><strong>Matric. no.:</strong> {{ $student->matric_number ?? 'NIL' }}
                </div>
                <div class="border px-2 py-0.5"><strong>Level:</strong> {{ $level ?? 'NIL' }}</div>
                <div class="border px-2 py-0.5"><strong>Phone:</strong> {{ $student->phone ?? 'NIL' }}</div>
                <div class="border px-2 py-0.5"><strong>Semester:</strong> {{ $semester ?? 'NIL' }}</div>
                <div class="border px-2 py-0.5"><strong>Gender:</strong> {{ $student->gender ?? 'NIL' }}</div>
                <div class="border px-2 py-0.5"><strong>Department:</strong> {{ $department ?? 'NIL' }}</div>
            </div>
        </div>

        {{-- RESULT TABLE --}}
        <x-result :assessmentNames="$assessmentNames" :courseData="$courseData"
            :semesterTotalCreditUnit="$semesterTotalCreditUnit" :semesterTotalGradePoint="$semesterTotalGradePoint"
            :semesterGradePointAverage="$semesterGradePointAverage" :semesterGradeRemark="$semesterGradeRemark"
            :semesterOutstandingCourses="$semesterOutstandingCourses"
            :cumulativeTotalCreditUnit="$cumulativeTotalCreditUnit"
            :cumulativeTotalGradePoint="$cumulativeTotalGradePoint"
            :cumulativeGradePointAverage="$cumulativeGradePointAverage" :cumulativeGradeRemark="$cumulativeGradeRemark"
            :cumulativeOutstandingCourses="$cumulativeOutstandingCourses" :hasUnpaidPortalFee="false"
            :isExport="true" />

        {{-- PRINT BUTTON --}}
        <div class="fixed bottom-4 right-4 print:hidden">
            <x-filament::button tag="button" color="primary" icon="heroicon-o-printer" onclick="window.print()">
                Print result
            </x-filament::button>
        </div>

        <script>
            window.onload = function() {
                window.print();
            }
        </script>
    </div>

    @livewireScripts
    @filamentScripts
</body>

</html>