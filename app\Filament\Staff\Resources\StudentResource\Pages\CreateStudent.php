<?php

namespace App\Filament\Staff\Resources\StudentResource\Pages;

use App\Enums\Role;
use App\Models\User;
use App\Models\Semester;
use App\Models\SchoolSession;
use App\Enums\AdmissionStatus;
use App\Enums\ScreeningStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Log;
use App\Services\CodeGenerationService;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Staff\Resources\StudentResource;

class CreateStudent extends CreateRecord
{
    protected static string $resource = StudentResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $data['role'] = Role::STUDENT;

        $data['application']['number'] = (new CodeGenerationService())->generateApplicationNumber();
        $data['application']['admission_status'] = AdmissionStatus::PENDING;
        $data['application']['screening_status'] = ScreeningStatus::PENDING;

        $data['registrations']['school_session_id'] = $data['application']['school_session_id'];
        $data['registrations']['programme_id'] = $data['application']['programme_id'];
        $data['registrations']['semester_id'] = activeSemester()->id ?? Semester::first()->id;
        // $data['registrations']['level_id'] = Level::first()->id;
        $data['registrations']['is_active'] = false;

        $record = DB::transaction(function () use ($data) {
            $userData = collect($data)->except(['application', 'registrations', 'guardian'])->toArray();

            $record = static::getModel()::create($userData);

            if (isset($data['guardian'])) {
                $record->guardian()->create($data['guardian']);
            }

            if (isset($data['application'])) {
                $record->application()->create($data['application']);
            }

            if (isset($data['registrations'])) {
                $record->registrations()->create($data['registrations']);
            }

            return $record;
        });

        if (!$record) {
            Log::error('Student creation failed.', [
                'data' => $data,
            ]);

            Notification::make()
                ->title('Operation Failed')
                ->body('Student creation failed. Please try again. If the issue persists, please contact support.')
                ->danger()
                ->send();

            throw new \Exception('Student creation failed.');
        }

        $semester = Semester::find($record->registrations->first()->semester_id);
        $session = SchoolSession::find($record->registrations->first()->school_session_id);

        User::whereIn('role', management_staff_roles())->get()->each(function ($recipient) use ($record, $semester, $session) {
            try {
                Notification::make()
                    ->title('Application Received')
                    ->icon('heroicon-o-check-circle')
                    ->iconColor('success')
                    ->body(new HtmlString("You have successfully submitted an application for <b>{$record->name}</b> for <b>{$semester->name}</b>, <b>{$session->name}</b>."))
                    ->sendToDatabase($recipient);
            } catch (\Exception $e) {
                Log::error('Application received notification failed: ' . $e->getMessage());
            }
        });

        return $record;
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Student Created')
            ->body('The student has been registered successfully and is pending approval.');
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index', [
            'activeTab' => 'pending',
        ]);
    }
}
