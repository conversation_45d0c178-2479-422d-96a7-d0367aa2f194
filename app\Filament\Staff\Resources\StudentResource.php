<?php

namespace App\Filament\Staff\Resources;

use App\Enums\AdmissionStatus;
use App\Enums\Role;
use App\Enums\ScreeningStatus;
use App\Filament\Components\StudentForm;
use App\Filament\Staff\Resources\StudentResource\Pages;
use App\Models\Course;
use App\Models\Grade;
use App\Models\Level;
use App\Models\Programme;
use App\Models\Registration;
use App\Models\SchoolSession;
use App\Models\Semester;
use App\Models\TotalScore;
use App\Models\User;
use App\Services\CodeGenerationService;
use App\Settings\AdmissionSettings;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Support\Enums\ActionSize;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\ColumnGroup;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\HtmlString;
use Illuminate\View\View;
use Livewire\Component;

class StudentResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $modelLabel = 'student';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationGroup = 'User';

    protected static ?string $navigationBadgeTooltip = 'Total number of registered students';

    public static function canAccess(): bool
    {
        return management_staff_access();
    }

    public static function canEdit($record): bool
    {
        return main_staff_access();
    }

    public static function canDelete($record): bool
    {
        return ict_access();
    }

    public static function canCreate(): bool
    {
        return ict_access();
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with([
                'registrations' => fn ($query) => $query->with(['programme', 'level', 'semester', 'schoolSession', 'portalInvoice']),
                'application',
                'guardian',
            ])
            ->withMax('registrations as latest_registration_at', 'created_at')
            ->whereHas('registrations')
            ->where('users.role', Role::STUDENT);
    }

    public static function getNavigationBadge(): ?string
    {
        return User::where('role', Role::STUDENT)
            ->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema(
                StudentForm::schema(),
            );
    }

    public static function table(Table $table): Table
    {

        return $table
            ->description(function ($livewire) {
                $tab = $livewire->activeTab;

                return match ($tab) {
                    'registered' => new HtmlString('These are students whose admissions have been approved or registered for the current session.<br>
                        <b>NOTE:</b> To use bulk actions like <b>register</b>, <b>graduate</b>, and <b>withdraw</b>. Make sure to select <b>session</b>, <b>semester</b>, <b>level</b>, and <b>programme</b>.'),
                    'pending' => ('These applicants are newly registered and awaiting screening results and final approval. Please review and update their status.'),
                    'denied' => ('These are applicants denied admission. Their records will be automatically deleted after 30 days.'),
                    'graduated' => ('These students have graduated. Their records are retained in the system.'),
                    'withdrawn' => ('These students have withdrawn. Their records are retained in the system.'),
                    default => null,
                };
            })
            ->deferLoading()
            ->deferFilters()
            ->persistFiltersInSession()
            ->striped()
            ->paginated([10, 20, 30, 40, 50])
            ->recordUrl(null)
            ->recordAction(null)
            ->defaultSort('latest_registration_at', 'desc')
            ->emptyStateHeading('No Students Yet')
            ->emptyStateDescription(fn ($livewire) => match ($livewire->activeTab) {
                'registered' => new HtmlString('Once you register your first student, select <b>session</b> and <b>semester</b> at least to view students.'),
                'pending' => new HtmlString('Once you create your first student, select <b>session</b> and <b>semester</b> at least to view students.'),
                'denied' => new HtmlString('Once you deny a student\'s admission, select <b>session</b> and <b>semester</b> at least to view students.'),
                'graduated' => new HtmlString('Once you graduate a student, select <b>session</b> and <b>semester</b> at least to view students.'),
                'withdrawn' => new HtmlString('Once you withdraw a student, select <b>session</b> and <b>semester</b> at least to view students.')
            })
            ->searchPlaceholder('Search (student name)')

            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                ImageColumn::make('photo')
                    ->defaultImageUrl(fn ($record) => 'https://ui-avatars.com/api/?name='.urlencode($record->name))
                    ->extraImgAttributes(['style' => 'border-radius: 0.125rem;'])
                    ->size('3rem'),
                TextColumn::make('name')
                    ->searchable(['last_name', 'first_name', 'middle_name'])
                    ->sortable(
                        query: fn ($query, $direction) => $query->orderBy('users.last_name', $direction)
                    ),
                TextColumn::make('matric_number')
                    ->label('Matric. no.')
                    ->hidden(fn ($livewire) => $livewire->activeTab === 'pending'),
                TextColumn::make('application.number')
                    ->label('Application number')
                    ->visible(fn ($livewire) => $livewire->activeTab === 'pending'),
                TextColumn::make('activeLevel.name')
                    ->hidden(fn ($livewire) => $livewire->activeTab === 'pending')
                    ->label('Level'),
                TextColumn::make('activeProgramme.name')
                    ->visible(fn ($livewire) => $livewire->activeTab === 'registered')
                    ->label('Programme'),
                TextColumn::make('application.programme.name')
                    ->hidden(fn ($livewire) => $livewire->activeTab === 'registered')
                    ->label('Entry programme'),
                ColumnGroup::make('Entrance screening', [
                    TextInputColumn::make('application.screening_score')
                        ->visible(fn ($livewire) => $livewire->activeTab === 'pending')
                        ->disabled(fn ($livewire) => $livewire->activeTab === 'denied')
                        ->rules(function () {
                            $maxScore = app(AdmissionSettings::class)->screening_max_score;

                            return ['numeric', 'min:0', "max:{$maxScore}"];
                        })
                        ->extraAttributes([
                            'style' => 'width: 80px !important; min-width: 80px !important; max-width: 80px !important;',
                        ])
                        // ->type('number') updates quick on blur
                        ->label('Score')
                        ->afterStateUpdated(function ($state, $record) {
                            $cutOffMark = app(AdmissionSettings::class)->screening_cut_off_mark;

                            $record->application->update([
                                'screening_score' => $state,
                                'screening_status' => match (true) {
                                    ! isset($state) => ScreeningStatus::PENDING,
                                    $state >= $cutOffMark => ScreeningStatus::PASSED,
                                    default => ScreeningStatus::FAILED,
                                },
                            ]);
                        }),
                    TextColumn::make('application.screening_status')
                        ->badge()
                        ->label('Status')
                        ->visible(fn ($livewire) => $livewire->activeTab === 'pending'),
                ])->alignment(Alignment::Center),
            ])

            ->filters([
                SelectFilter::make('session_semester_level_programme_filter')
                    ->form([
                        Select::make('school_session_id')
                            ->required()
                            ->label('Session')
                            ->native(false)
                            ->options(function () {
                                return SchoolSession::query()
                                    ->orderBy('name', 'desc')
                                    ->pluck('name', 'id');
                            })
                            ->default(activeSchoolSession()?->id),
                        Select::make('semester_id')
                            ->required()
                            ->label('Semester')
                            ->native(false)
                            ->options(function () {
                                return Semester::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            })
                            ->default(activeSemester()?->id),
                        Select::make('level_id')
                            ->label('Level')
                            ->native(false)
                            ->options(function () {
                                return Level::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            }),
                        Select::make('programme_id')
                            ->label('Programme')
                            ->native(false)
                            ->searchable()
                            ->options(function () {
                                return Programme::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            }),
                    ])
                    ->columns(4)
                    ->baseQuery(function (Builder $query, array $data): Builder {
                        if (empty($data['school_session_id']) || empty($data['semester_id'])) {
                            return $query->whereRaw('1 = 0');
                        }

                        return $query;
                    })
                    ->query(
                        fn (Builder $query, array $data) => $query->whereHas('registrations', function ($q) use ($data) {
                            $q->where('school_session_id', $data['school_session_id'])
                                ->where('semester_id', $data['semester_id'])
                                ->when(isset($data['level_id']), fn ($q) => $q->where('level_id', $data['level_id']))
                                ->when(isset($data['programme_id']), fn ($q) => $q->where('programme_id', $data['programme_id']));
                        })
                    )
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($sessionId = $data['school_session_id'] ?? null) {
                            if ($name = SchoolSession::find($sessionId)?->name) {
                                $indicators[] = Indicator::make("Session: {$name}")->removable(false);
                            }
                        }

                        if ($semesterId = $data['semester_id'] ?? null) {
                            if ($name = Semester::find($semesterId)?->name) {
                                $indicators[] = Indicator::make("Semester: {$name}")->removable(false);
                            }
                        }

                        if ($levelId = $data['level_id'] ?? null) {
                            if ($name = Level::find($levelId)?->name) {
                                $indicators[] = Indicator::make("Level: {$name}");
                            }
                        }

                        if ($programmeId = $data['programme_id'] ?? null) {
                            if ($name = Programme::find($programmeId)?->name) {
                                $indicators[] = Indicator::make("Programme: {$name}");
                            }
                        }

                        return $indicators;
                    }),

            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(1)
            ->filtersApplyAction(
                fn (Action $action) => $action->label('View students'),
            )

            ->actions([
                ActionGroup::make([
                    ViewAction::make()
                        ->label('View student'),
                    EditAction::make()
                        ->label('Edit bio-data')
                        ->hidden(fn ($livewire) => $livewire->activeTab === 'denied'),
                    Action::make('approveAdmission')
                        ->visible(
                            fn ($livewire, $record) => $livewire->activeTab === 'pending' &&
                                $record->application?->screening_status === ScreeningStatus::PASSED &&
                                main_staff_access()
                        )
                        ->label('Approve admission')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading('Approve Admission?')
                        ->modalDescription(new HtmlString("This will confirm the student's registration before finalizing their admission. Are you sure you want to approve?"))
                        ->modalSubmitActionLabel('Approve')
                        ->fillForm(function (User $record): array {
                            return [
                                'school_session_id' => $record->registrations()->first()?->school_session_id,
                                'semester_id' => activeSemester()?->id,
                                'level_id' => $record->registrations()->first()?->level_id,
                                'programme_id' => $record->registrations()->first()?->programme_id,
                            ];
                        })
                        ->form([
                            Section::make('Student registration')
                                ->schema([
                                    Select::make('school_session_id')
                                        ->required()
                                        ->label('Session')
                                        ->native(false)
                                        ->options(function () {
                                            return SchoolSession::query()
                                                ->orderBy('name', 'desc')
                                                ->pluck('name', 'id');
                                        }),
                                    Select::make('semester_id')
                                        ->required()
                                        ->label('Semester')
                                        ->native(false)
                                        ->options(function () {
                                            return Semester::query()
                                                ->orderBy('name')
                                                ->pluck('name', 'id');
                                        }),
                                    Select::make('level_id')
                                        ->required()
                                        ->label('Level')
                                        ->native(false)
                                        ->options(function () {
                                            return Level::query()
                                                ->orderBy('name')
                                                ->pluck('name', 'id');
                                        }),
                                    Select::make('programme_id')
                                        ->required()
                                        ->label('Programme')
                                        ->native(false)
                                        ->searchable()
                                        ->options(function () {
                                            return Programme::query()
                                                ->orderBy('name')
                                                ->pluck('name', 'id');
                                        }),
                                ])
                                ->columns(4),
                        ])
                        ->modalWidth(MaxWidth::FiveExtraLarge)
                        ->action(function (User $record, $data) {
                            DB::transaction(function () use ($record, $data) {
                                $record->update([
                                    'matric_number' => (new CodeGenerationService)->generateMatriculationNumber($data['school_session_id'], $data['programme_id']),
                                ]);

                                $record->application()->update([
                                    'admission_status' => AdmissionStatus::APPROVED,
                                    'admission_date' => now(),
                                    'school_session_id' => $data['school_session_id'],
                                ]);

                                if ($record->registrations()->exists()) {
                                    $registration = $record->registrations()->first();

                                    $registration->update([
                                        'school_session_id' => $data['school_session_id'],
                                        'semester_id' => $data['semester_id'],
                                        'level_id' => $data['level_id'],
                                        'programme_id' => $data['programme_id'],
                                        'is_active' => true,
                                        'created_at' => now(),
                                    ]);
                                }
                                if ($record->invoices()->exists()) {
                                    $invoice = $record->invoices()->first();

                                    $invoice->update([
                                        'payable_id' => $registration->id,
                                        'payable_type' => Registration::class,
                                    ]);
                                }

                                Notification::make()
                                    ->title('Admission Approved')
                                    ->icon('heroicon-o-check-circle')
                                    ->iconColor('success')
                                    ->body('Your application has been approved successfully.')
                                    ->sendToDatabase($record);

                                Notification::make()
                                    ->success()
                                    ->title('Admission Approved')
                                    ->body("The student's admission has been approved successfully.")
                                    ->send();
                            });
                        }),
                    Action::make('denyAdmission')
                        ->label('Deny admission')
                        ->visible(
                            fn ($livewire, $record) => $livewire->activeTab === 'pending' &&
                                main_staff_access() &&
                                in_array($record->application?->screening_status, [ScreeningStatus::FAILED, ScreeningStatus::PENDING, ScreeningStatus::PASSED])
                        )
                        ->icon('heroicon-o-x-circle')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->modalHeading('Deny Admission?')
                        ->modalDescription('Are you sure you want to deny admission for this student? This cannot be undone.')
                        ->modalSubmitActionLabel('Deny')
                        ->action(function (User $record) {
                            $record->application()->update(['admission_status' => AdmissionStatus::DENIED]);

                            Notification::make()
                                ->title('Admission denied')
                                ->icon('heroicon-o-x-circle')
                                ->iconColor('danger')
                                ->body('Sorry, your application was denied.')
                                ->sendToDatabase($record);

                            Notification::make()
                                ->success()
                                ->title('Admission Denied')
                                ->body("The student's admission was denied.")
                                ->send();
                        }),

                    Action::make('resetPassword')
                        ->label('Reset password')
                        ->visible(fn ($livewire) => main_staff_access() && $livewire->activeTab !== 'denied')
                        ->icon('heroicon-o-arrow-path')
                        ->requiresConfirmation()
                        ->modalHeading('Reset Password?')
                        ->modalDescription(fn ($record) => new HtmlString("Are you sure you want to reset {$record->name}'s login password to <strong>1234</strong>? This action can't be undone."))
                        ->modalSubmitActionLabel('Reset')
                        ->action(function (User $record) {
                            $record->update(['password' => bcrypt('1234')]);

                            Notification::make()
                                ->title('Password Reset')
                                ->icon('heroicon-o-arrow-path')
                                ->body('Your login password was reset successfully.')
                                ->sendToDatabase($record);

                            Notification::make()
                                ->success()
                                ->title('Password Reset')
                                ->body("The student's login password has been reset successfully.")
                                ->send();
                        }),
                    Action::make('register')
                        ->visible(fn ($livewire) => $livewire->activeTab === 'registered' && main_staff_access())
                        ->icon('heroicon-o-arrows-up-down')
                        ->color('info')
                        ->modalHeading('Student registration')
                        ->modalDescription(new HtmlString('Register this student to a different <b>session</b>, <b>semester</b>, <b>level</b>, or <b>programme</b>.'))
                        ->modalSubmitAction(false)
                        ->fillForm(function (User $record): array {

                            return [
                                'school_session_id' => $record->activeRegistration()->first()?->school_session_id,
                                'semester_id' => $record->activeRegistration()->first()?->semester_id,
                                'level_id' => $record->activeRegistration()->first()?->level_id,
                                'programme_id' => $record->activeRegistration()->first()?->programme_id,
                                'is_active' => true,
                            ];
                        })
                        ->form([
                            Section::make('Student registration')
                                ->schema([
                                    Select::make('school_session_id')
                                        ->required()
                                        ->label('Session')
                                        ->native(false)
                                        ->options(function () {
                                            return SchoolSession::query()
                                                ->orderBy('name', 'desc')
                                                ->pluck('name', 'id');
                                        }),
                                    Select::make('semester_id')
                                        ->required()
                                        ->label('Semester')
                                        ->native(false)
                                        ->options(function () {
                                            return Semester::query()
                                                ->orderBy('name')
                                                ->pluck('name', 'id');
                                        }),
                                    Select::make('level_id')
                                        ->required()
                                        ->label('Level')
                                        ->native(false)
                                        ->options(function () {
                                            return Level::query()
                                                ->orderBy('name')
                                                ->pluck('name', 'id');
                                        }),
                                    Select::make('programme_id')
                                        ->required()
                                        ->label('Programme')
                                        ->native(false)
                                        ->searchable()
                                        ->options(function () {
                                            return Programme::query()
                                                ->orderBy('name')
                                                ->pluck('name', 'id');
                                        }),
                                    Checkbox::make('is_active')
                                        ->inline(false)
                                        ->label('Set as active')
                                        ->default(false),
                                ])
                                ->columns(4),
                        ])
                        ->modalContentFooter(fn ($record): View => view(
                            'filament.tables.registrations',
                            [
                                'registrations' => $record->registrations->sortByDesc('created_at')->values(),
                            ],
                        ))
                        ->modalWidth(MaxWidth::FiveExtraLarge)
                        ->extraModalFooterActions([
                            Action::make('registerStudent')
                                ->requiresConfirmation()
                                ->cancelParentActions()
                                ->modalHeading('Confirm Registration?')
                                ->modalWidth(MaxWidth::FiveExtraLarge)
                                ->modalDescription(function (Component $livewire, $record) {
                                    $data = $livewire->mountedTableActionsData[0];

                                    $name = $record->name;

                                    $newSession = SchoolSession::find($data['school_session_id'])?->name ?? '';
                                    $newSemester = Semester::find($data['semester_id'])?->name ?? '';
                                    $newLevel = Level::find($data['level_id'])?->name ?? '';
                                    $newProgramme = Programme::find($data['programme_id'])?->name ?? '';

                                    $existingRegistration = $record->registrations
                                        ->where('school_session_id', $data['school_session_id'])
                                        ->where('semester_id', $data['semester_id'])
                                        ->first();

                                    if ($existingRegistration) {
                                        $currentLevel = Level::find($existingRegistration->level_id)?->name ?? '';
                                        $currentProgramme = Programme::find($existingRegistration->programme_id)?->name ?? '';

                                        return new HtmlString("
                                        Are you sure you want to update <b>{$name}</b>'s registration from:
                                        <br><br><b>{$currentLevel}</b> level, <b>{$currentProgramme}</b> programme
                                        <br><br>to <b>{$newLevel}</b> level, <b>{$newProgramme}</b> programme
                                        <br><br>in <b>{$newSemester}</b> of <b>{$newSession}</b> session?
                                        <br><br><i>This action cannot be undone.</i>
                                    ");
                                    }

                                    return new HtmlString("
                                    Are you sure you want to register <b>{$name}</b> for:
                                    <br><br><b>{$newLevel}</b> level, <b>{$newProgramme}</b> programme
                                    <br><br>in <b>{$newSemester}</b> of <b>{$newSession}</b> session?
                                    <br><br><i>This action cannot be undone.</i>
                                ");
                                })
                                ->action(function (Component $livewire, $record, $action) {
                                    $formData = $livewire->mountedTableActionsData[0];

                                    DB::transaction(function () use ($record, $formData, $action) {

                                        $alreadyRegistered = $record->registrations()
                                            ->where('school_session_id', $formData['school_session_id'])
                                            ->where('semester_id', $formData['semester_id'])
                                            ->where('level_id', $formData['level_id'])
                                            ->where('programme_id', $formData['programme_id'])
                                            ->exists();

                                        if ($alreadyRegistered) {
                                            Notification::make()
                                                ->danger()
                                                ->title('Operation Failed')
                                                ->body(new HtmlString('The student is already registered in the selected <b>session</b>, <b>semester</b>, <b>level</b>, and <b>programme</b>.'))
                                                ->send();

                                            $action->halt();
                                        }

                                        $sameSessionAndSemester = $record->registrations()
                                            ->where('school_session_id', $formData['school_session_id'])
                                            ->where('semester_id', $formData['semester_id'])
                                            ->first();

                                        $newSession = SchoolSession::find($formData['school_session_id'])?->name ?? '';
                                        $newSemester = Semester::find($formData['semester_id'])?->name ?? '';
                                        $newLevel = Level::find($formData['level_id'])?->name ?? '';
                                        $newProgramme = Programme::find($formData['programme_id'])?->name ?? '';

                                        if (($formData['is_active'] ?? false) === true) {
                                            $record->registrations()
                                                ->when($sameSessionAndSemester, fn ($q) => $q->where('id', '!=', $sameSessionAndSemester->id))
                                                ->update(['is_active' => false]);
                                        }

                                        if ($sameSessionAndSemester) {
                                            $sameSessionAndSemester->update([
                                                'semester_id' => $formData['semester_id'],
                                                'level_id' => $formData['level_id'],
                                                'programme_id' => $formData['programme_id'],
                                                'is_active' => $formData['is_active'] ?? false,
                                            ]);

                                            Notification::make()
                                                ->title('Registration Updated')
                                                ->icon('heroicon-o-check-circle')
                                                ->iconColor('success')
                                                ->body("Your registration was updated for <b>{$newSession}</b> session, <b>{$newSemester}</b>, <b>{$newLevel}</b> level, and <b>{$newProgramme}</b> programme successfully.")
                                                ->sendToDatabase($record);

                                            Notification::make()
                                                ->success()
                                                ->title('Registration Updated')
                                                ->body("Student registration updated for <b>{$newSession}</b> session, <b>{$newSemester}</b>, <b>{$newLevel}</b> level, and <b>{$newProgramme}</b> programme successfully.")
                                                ->send();
                                        } else {
                                            $record->registrations()->create($formData);

                                            Notification::make()
                                                ->title('New Registration')
                                                ->icon('heroicon-o-check-circle')
                                                ->iconColor('success')
                                                ->body("You were registered for <b>{$newSession}</b> session, <b>{$newSemester}</b>, <b>{$newLevel}</b> level, and <b>{$newProgramme}</b> programme successfully.")
                                                ->sendToDatabase($record);

                                            Notification::make()
                                                ->success()
                                                ->title('Student Registered')
                                                ->body("Student registered to <b>{$newSession}</b> session, <b>{$newSemester}</b>, <b>{$newLevel}</b> level, and <b>{$newProgramme}</b> programme successfully.")
                                                ->send();
                                        }
                                    });
                                }),
                        ]),

                    Action::make('graduate')
                        ->label('Graduate')
                        ->visible(fn ($livewire, $record) => $livewire->activeTab === 'registered' && main_staff_access() && ! $record->registrations()->where('is_graduated', true)->exists())
                        ->icon('heroicon-o-academic-cap')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading('Graduate student?')
                        ->modalDescription(new HtmlString(
                            "Are you sure you'd like to graduate this student? The student will not have active registration but their records will remain in the portal. <br><i>This cannot be undone.</i>"
                        ))
                        ->modalSubmitActionLabel('Yes, graduate')
                        ->action(function (User $record, $livewire, $action) {
                            DB::transaction(function () use ($record, $livewire, $action) {
                                $filters = self::extractFilters($livewire);
                                $filteredSessionId = $filters['school_session_id'] ?? null;
                                $filteredSemesterId = $filters['semester_id'] ?? null;

                                // Blocker 1: Minimum semesters
                                if ($record->registrations()->count() < 6) {
                                    Notification::make()
                                        ->danger()
                                        ->title('Graduation Failed')
                                        ->body('Student must have at least 6 semesters before graduation.')
                                        ->send();
                                    $action->halt();
                                }

                                // Blocker 2: Outstanding courses
                                $outstandingCourses = self::getCumulativeOutstandingCourses($record);
                                if ($outstandingCourses->isNotEmpty()) {
                                    $coursesList = $outstandingCourses->pluck('code')->join(', ');
                                    Notification::make()
                                        ->danger()
                                        ->title('Graduation Failed')
                                        ->body(new HtmlString("Outstanding courses must be cleared before graduation:<br><b>{$coursesList}</b>"))
                                        ->persistent()
                                        ->send();
                                    $action->halt();
                                }

                                // Blocker 3: If already graduated
                                if ($record->registrations()->where('is_graduated', true)->exists()) {
                                    Notification::make()
                                        ->danger()
                                        ->title('Graduation Failed')
                                        ->body('Student has already been graduated.')
                                        ->send();
                                    $action->halt();
                                }

                                // Mark graduation
                                if ($filteredSessionId && $filteredSemesterId) {
                                    $record->registrations()
                                        ->where('school_session_id', $filteredSessionId)
                                        ->where('semester_id', $filteredSemesterId)
                                        ->update(['is_graduated' => true]);
                                }

                                // Deactivate active registrations only
                                $record->registrations()
                                    ->where('is_active', true)
                                    ->update(['is_active' => false]);

                                // Notify student
                                if (! $record->hasUnpaidPortalFee()) {
                                    Notification::make()
                                        ->title('Graduation Successful')
                                        ->icon('heroicon-o-academic-cap')
                                        ->iconColor('success')
                                        ->body('You have successfully graduated from the college. Congratulations!')
                                        ->sendToDatabase($record);
                                }

                                // Notify staff
                                Notification::make()
                                    ->success()
                                    ->title('Student Graduated')
                                    ->body('The student has been graduated successfully.')
                                    ->send();
                            });
                        }),
                    Action::make('withdraw')
                        ->label('Withdraw')
                        ->visible(fn ($livewire) => $livewire->activeTab === 'registered' && main_staff_access())
                        ->icon('heroicon-o-minus-circle')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->modalHeading('Withdraw student?')
                        ->modalDescription(new HtmlString("Are you sure you'd like to withdraw this student from school portal? The student will not have active registration but his/her records will remain in the portal and will have access to them. <br><i>This cannot be undone.</i>"))
                        ->modalSubmitActionLabel('Yes, withdraw')
                        ->action(function (User $record, $livewire) {

                            $filters = self::extractFilters($livewire);
                            $filteredSessionId = $filters['school_session_id'] ?? null;

                            $record->registrations()
                                ->where('school_session_id', $filteredSessionId)
                                ->update([
                                    'is_withdrawn' => true,
                                    'is_active' => false,
                                ]);

                            Notification::make()
                                ->title('Admission Withdrawn')
                                ->iconColor('danger')
                                ->icon('heroicon-o-minus-circle')
                                ->body('You were withdrawn from the college.')
                                ->sendToDatabase($record);

                            Notification::make()
                                ->success()
                                ->title('Student Withdrawn')
                                ->body('The student has been withdrawn successfully.')
                                ->send();
                        }),
                    DeleteAction::make()
                        ->visible(fn () => main_staff_access())
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Student Deleted')
                                ->body('The student has been deleted.'),
                        ),
                ])
                    ->tooltip('Student actions')
                    ->size(ActionSize::Large),

                ActionGroup::make([
                    ActionGroup::make([
                        Tables\Actions\Action::make('printApplicationData')
                            ->label('Print')
                            ->icon('heroicon-m-printer')
                            ->action(function ($record, $livewire) {
                                try {
                                    // Generate signed URL
                                    $url = URL::signedRoute('application-data.print', ['student' => $record->id]);

                                    $livewire->js("(function() {
                                        const newWindow = window.open(
                                            '$url',
                                            'ApplicationData',
                                            'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                                        );
                                    
                                    if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                                        alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                                    } else {
                                        newWindow.focus();
                                    }
                                    })();");
                                } catch (\Exception $e) {
                                    Log::error('Application data print failed:', ['error' => $e->getMessage()]);
                                    Notification::make()
                                        ->title('Print Error')
                                        ->body('Unable to print application data. Please try again later.')
                                        ->danger()
                                        ->send();
                                }
                            }),
                        Tables\Actions\Action::make('downloadApplicationData')
                            ->label('Download')
                            ->icon('heroicon-m-document-arrow-down')
                            ->action(function ($record, $livewire) {
                                try {
                                    $url = URL::signedRoute('application-data.download', ['student' => $record->id]);
                                    $livewire->js("window.location.href = '$url';");
                                } catch (\Exception $e) {
                                    Log::error('Application data download failed:', ['error' => $e->getMessage()]);
                                    Notification::make()
                                        ->title('Download Error')
                                        ->body('Unable to download application data. Please try again later.')
                                        ->danger()
                                        ->send();
                                }
                            }),
                    ])
                        ->link()
                        ->icon(false)
                        ->label('Application data')
                        ->extraAttributes([
                            'style' => 'margin-left: 0.75rem !important; margin-bottom: 0.5rem !important;  margin-top: 0.5rem !important;',
                        ]),

                    ActionGroup::make([
                        Tables\Actions\Action::make('printAdmissionLetter')
                            ->label('Print')
                            ->icon('heroicon-m-printer')
                            ->action(function ($record, $livewire) {
                                try {
                                    // Generate signed URL
                                    $url = URL::signedRoute('admission-letter.print', ['student' => $record->id]);

                                    $livewire->js("(function() {
                                        const newWindow = window.open(
                                            '$url',
                                            'AdmissionLetter',
                                            'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                                        );
                                    
                                        if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                                            alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                                        } else {
                                            newWindow.focus();
                                        }
                                    })();");
                                } catch (\Exception $e) {
                                    Log::error('Admission letter print failed:', ['error' => $e->getMessage()]);
                                    Notification::make()
                                        ->title('Print Error')
                                        ->body('Unable to print admission letter. Please try again later.')
                                        ->danger()
                                        ->send();
                                }
                            }),
                        Tables\Actions\Action::make('downloadAdmissionLetter')
                            ->label('Download')
                            ->icon('heroicon-m-document-arrow-down')
                            ->action(function ($record, $livewire) {
                                try {
                                    $url = URL::signedRoute('admission-letter.download', ['student' => $record->id]);
                                    $livewire->js("window.location.href = '$url';");
                                } catch (\Exception $e) {
                                    Log::error('Admission letter download failed:', ['error' => $e->getMessage()]);
                                    Notification::make()
                                        ->title('Download Error')
                                        ->body('Unable to download admission letter. Please try again later.')
                                        ->danger()
                                        ->send();
                                }
                            }),
                    ])
                        ->visible(fn ($record) => ($record->application?->admission_status === AdmissionStatus::APPROVED))
                        ->link()
                        ->icon(false)
                        ->label('Admission letter')
                        ->extraAttributes([
                            'style' => 'margin-left: 0.75rem !important; margin-bottom: 0.5rem !important;',
                        ]),

                    ActionGroup::make([
                        Tables\Actions\Action::make('printAcceptanceLetter')
                            ->label('Print')
                            ->icon('heroicon-m-printer')
                            ->action(function ($record, $livewire) {
                                try {
                                    // Generate signed URL
                                    $url = URL::signedRoute('acceptance-letter.print', ['student' => $record->id]);

                                    $livewire->js("(function() {
                                        const newWindow = window.open(
                                            '$url',
                                            'AcceptanceLetter',
                                            'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                                        );
                                    
                                        if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                                            alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                                        } else {
                                            newWindow.focus();
                                        }
                                    })();");
                                } catch (\Exception $e) {
                                    Log::error('Acceptance letter print failed:', ['error' => $e->getMessage()]);
                                    Notification::make()
                                        ->title('Print Error')
                                        ->body('Unable to print acceptance letter. Please try again later.')
                                        ->danger()
                                        ->send();
                                }
                            }),
                        Tables\Actions\Action::make('downloadAcceptanceLetter')
                            ->label('Download')
                            ->icon('heroicon-m-document-arrow-down')
                            ->action(function ($record, $livewire) {
                                try {
                                    $url = URL::signedRoute('acceptance-letter.download', ['student' => $record->id]);
                                    $livewire->js("window.location.href = '$url';");
                                } catch (\Exception $e) {
                                    Log::error('Acceptance letter download failed:', ['error' => $e->getMessage()]);
                                    Notification::make()
                                        ->title('Download Error')
                                        ->body('Unable to download acceptance letter. Please try again later.')
                                        ->danger()
                                        ->send();
                                }
                            }),
                    ])
                        ->visible(fn ($record) => ($record->application?->admission_status === AdmissionStatus::APPROVED))
                        ->link()
                        ->icon(false)
                        ->label('Acceptance letter')
                        ->extraAttributes([
                            'style' => 'margin-left: 0.75rem !important; margin-bottom: 0.5rem !important;',
                        ]),

                    ActionGroup::make([
                        Tables\Actions\Action::make('printBioData')
                            ->label('Print')
                            ->icon('heroicon-m-printer')
                            ->action(function ($record, $livewire) {
                                try {
                                    // Generate signed URL
                                    $url = URL::signedRoute('bio-data.print', ['student' => $record->id]);

                                    $livewire->js("(function() {
                                const newWindow = window.open(
                                    '$url',
                                    'BioData',
                                    'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                                );
                            
                                if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                                    alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                                } else {
                                    newWindow.focus();
                                }
                            })();");
                                } catch (\Exception $e) {
                                    Log::error('Bio data print failed:', ['error' => $e->getMessage()]);
                                    Notification::make()
                                        ->title('Print Error')
                                        ->body('Unable to print bio-data. Please try again later.')
                                        ->danger()
                                        ->send();
                                }
                            }),
                        Tables\Actions\Action::make('downloadBioData')
                            ->label('Download')
                            ->icon('heroicon-m-document-arrow-down')
                            ->action(function ($record, $livewire) {
                                try {
                                    $url = URL::signedRoute('bio-data.download', ['student' => $record->id]);
                                    $livewire->js("window.location.href = '$url';");
                                } catch (\Exception $e) {
                                    Log::error('Bio data download failed:', ['error' => $e->getMessage()]);
                                    Notification::make()
                                        ->title('Download Error')
                                        ->body('Unable to download bio-data. Please try again later.')
                                        ->danger()
                                        ->send();
                                }
                            }),
                    ])
                        ->visible(fn ($record) => ($record->application?->admission_status === AdmissionStatus::APPROVED))
                        ->link()
                        ->icon(false)
                        ->label('Bio-data')
                        ->extraAttributes([
                            'style' => 'margin-left: 0.75rem !important; margin-bottom: 0.5rem !important;',
                        ]),

                ])->icon('heroicon-m-document-text')
                    ->tooltip('Export documents')
                    ->size(ActionSize::Large),
            ])

            ->bulkActions([
                BulkActionGroup::make([
                    BulkAction::make('register')
                        ->icon('heroicon-o-arrows-up-down')
                        ->color('info')
                        ->visible(function ($livewire) {
                            return $livewire->activeTab === 'registered'
                                && main_staff_access()
                                && self::hasRequiredFilters($livewire);
                        })
                        ->modalDescription(new HtmlString('Register these students to a different <b>session</b>, <b>semester</b>, <b>level</b>, or <b>programme</b>. This will create new registrations or update existing ones for the same session and semester.'))
                        ->label('Register selected')
                        ->modalSubmitActionLabel('Register students')
                        ->fillForm(function (Collection $records): array {
                            $activeRegistrations = $records->map(
                                fn ($student) => $student->activeRegistration()->first()
                            )->filter();

                            $sameSession = $activeRegistrations->pluck('school_session_id')->unique()->count() === 1;
                            $sameSemester = $activeRegistrations->pluck('semester_id')->unique()->count() === 1;
                            $sameLevel = $activeRegistrations->pluck('level_id')->unique()->count() === 1;
                            $sameProgramme = $activeRegistrations->pluck('programme_id')->unique()->count() === 1;

                            return [
                                'school_session_id' => $sameSession ? $activeRegistrations->first()?->school_session_id : null,
                                'semester_id' => $sameSemester ? $activeRegistrations->first()?->semester_id : null,
                                'level_id' => $sameLevel ? $activeRegistrations->first()?->level_id : null,
                                'programme_id' => $sameProgramme ? $activeRegistrations->first()?->programme_id : null,
                                'is_active' => true,
                            ];
                        })
                        ->form([
                            Section::make('Student registration')
                                ->schema([
                                    Select::make('school_session_id')
                                        ->required()
                                        ->label('Session')
                                        ->native(false)
                                        ->options(function () {
                                            return SchoolSession::query()
                                                ->orderBy('name', 'desc')
                                                ->pluck('name', 'id');
                                        }),
                                    Select::make('semester_id')
                                        ->required()
                                        ->label('Semester')
                                        ->native(false)
                                        ->options(function () {
                                            return Semester::query()
                                                ->orderBy('name')
                                                ->pluck('name', 'id');
                                        }),
                                    Select::make('level_id')
                                        ->required()
                                        ->label('Level')
                                        ->native(false)
                                        ->options(function () {
                                            return Level::query()
                                                ->orderBy('name')
                                                ->pluck('name', 'id');
                                        }),
                                    Select::make('programme_id')
                                        ->required()
                                        ->label('Programme')
                                        ->native(false)
                                        ->searchable()
                                        ->options(function () {
                                            return Programme::query()
                                                ->orderBy('name')
                                                ->pluck('name', 'id');
                                        }),
                                    Checkbox::make('is_active')
                                        ->inline(false)
                                        ->label('Set as active')
                                        ->default(false),
                                ])
                                ->columns(4),
                        ])
                        ->modalWidth(MaxWidth::FiveExtraLarge)
                        ->action(function (Collection $records, $data, $livewire) {
                            $livewire->replaceMountedAction('confirmRegistrations', arguments: [
                                'registrationData' => $data,
                                'tableRecords' => $records->toArray(),
                            ]);
                        })
                        ->deselectRecordsAfterCompletion(),
                    BulkAction::make('graduate')
                        ->icon('heroicon-o-academic-cap')
                        ->color('success')
                        ->visible(function ($livewire) {

                            return $livewire->activeTab === 'registered'
                                && main_staff_access()
                              && self::hasRequiredFilters($livewire);
                        })
                        ->label('Graduate selected')
                        ->action(function (Collection $records, $livewire) {
                            $filters = self::extractFilters($livewire);
                            $filteredSessionId = $filters['school_session_id'] ?? null;
                            $filteredSemesterId = $filters['semester_id'] ?? null;
                            $filteredLevelId = $filters['level_id'] ?? null;
                            $filteredProgrammeId = $filters['programme_id'] ?? null;
                            $sessionName = SchoolSession::find($filteredSessionId)?->name ?? 'Unknown Session';

                            $livewire->replaceMountedAction('confirmGraduations', arguments: [
                                'tableRecords' => $records->toArray(),
                                'filteredSessionId' => $filteredSessionId,
                                'filteredSemesterId' => $filteredSemesterId,
                                'filteredLevelId' => $filteredLevelId,
                                'filteredProgrammeId' => $filteredProgrammeId,
                                'sessionName' => $sessionName,
                            ]);
                        })
                        ->deselectRecordsAfterCompletion(),
                    BulkAction::make('withdraw')
                        ->icon('heroicon-o-minus-circle')
                        ->color('warning')
                        ->visible(function ($livewire) {
                            return $livewire->activeTab === 'registered'
                                && main_staff_access()
                                && self::hasRequiredFilters($livewire);
                        })
                        ->label('Withdraw selected')
                        ->action(function (Collection $records, $livewire) {
                            $filters = self::extractFilters($livewire);
                            $filteredSessionId = $filters['school_session_id'] ?? null;
                            $sessionName = SchoolSession::find($filteredSessionId)?->name ?? 'Unknown Session';

                            $livewire->replaceMountedAction('confirmWithdrawals', arguments: [
                                'tableRecords' => $records->toArray(),
                                'filteredSessionId' => $filteredSessionId,
                                'sessionName' => $sessionName,
                            ]);
                        })
                        ->deselectRecordsAfterCompletion(),
                ]),
            ]);
    }

    private static function extractFilters($livewire): array
    {
        $filters = $livewire->tableFilters['session_semester_level_programme_filter'] ?? [];

        return [
            'school_session_id' => $filters['school_session_id'] ?? null,
            'semester_id' => $filters['semester_id'] ?? null,
            'level_id' => $filters['level_id'] ?? null,
            'programme_id' => $filters['programme_id'] ?? null,
        ];
    }

    private static function hasRequiredFilters($livewire): bool
    {
        return ! in_array(null, self::extractFilters($livewire), true);
    }

    public static function getCourseData(HasTable $livewire, $record, $course)
    {
        $filters = self::extractFilters($livewire);
        $registration = self::getRegistration($record->id, $filters);

        if (! $registration) {
            return null;
        }

        $totalScore = TotalScore::where([
            'registration_id' => $registration->id,
            'course_id' => $course->id,
        ])->value('total');

        $grade = self::getGradeFromScore($totalScore);

        return [
            'total_score' => $totalScore,
            'grade' => $grade?->name,
            'point' => $grade?->point,
            'grade_point' => $grade ? $grade->point * $course->credit : null,
        ];
    }

    private static function getRegistration(int $userId, array $filters): ?Registration
    {
        return Registration::where([
            'user_id' => $userId,
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'level_id' => $filters['level_id'],
        ])->first();
    }

    public static function getGradeFromScore(?int $totalScore, ?Collection $grades = null)
    {
        if ($totalScore === null) {
            return null;
        }

        static $gradesCache;
        $gradesCache ??= Grade::all();

        return $gradesCache->first(
            fn ($grade) => $totalScore >= $grade->min_score && $totalScore <= $grade->max_score
        );
    }

    public static function getFailedScore(): int
    {
        static $failedScore;
        $failedScore ??= Grade::where('min_score', 0)->value('max_score');

        return $failedScore;
    }

    public static function getCourses(HasTable $livewire, $record)
    {
        $filters = self::extractFilters($livewire);

        // Get department from the student's programme
        $registration = self::getRegistration($record->id, $filters);
        if (! $registration) {
            return collect();
        }

        $programme = Programme::with(['firstDepartment', 'secondDepartment'])->find($registration->programme_id);
        if (! $programme) {
            return collect();
        }

        // Get courses from both departments if programme has two departments
        $departmentIds = collect([$programme->first_department_id, $programme->second_department_id])
            ->filter()
            ->toArray();

        return Course::select('id', 'code', 'title', 'credit', 'course_status')
            ->whereIn('department_id', $departmentIds)
            ->where('level_id', $filters['level_id'])
            ->where('semester_id', $filters['semester_id'])
            ->get();
    }

    public static function getCumulativeOutstandingCourses(User $record)
    {
        // NOTE: This version has no published filter
        $failedScore = self::getFailedScore();
        $registrations = $record->registrations;

        $registration = $registrations->first();
        if (! $registration) {
            return collect();
        }

        $programme = Programme::with(['firstDepartment', 'secondDepartment'])->find($registration->programme_id);
        if (! $programme) {
            return collect();
        }

        $departmentIds = collect([$programme->first_department_id, $programme->second_department_id])
            ->filter()
            ->toArray();

        // Collect all registered combinations of level, semester, and session (no published filter)
        $registeredCombos = $registrations->map(function ($reg) {
            return [
                'level_id' => $reg->level_id,
                'semester_id' => $reg->semester_id,
                'school_session_id' => $reg->school_session_id,
            ];
        });

        if ($registeredCombos->isEmpty()) {
            return collect();
        }

        // Fetch courses for those registered level + semester combos
        $courses = Course::select('id', 'code', 'title', 'level_id', 'semester_id')
            ->whereIn('department_id', $departmentIds)
            ->where(function ($query) use ($registeredCombos) {
                foreach ($registeredCombos as $combo) {
                    $query->orWhere(function ($sub) use ($combo) {
                        $sub->where('level_id', $combo['level_id'])
                            ->where('semester_id', $combo['semester_id']);
                    });
                }
            })
            ->get();

        // Filter failed courses
        $failedCourses = $courses->filter(function ($course) use ($record, $failedScore, $registeredCombos) {
            $combo = $registeredCombos->first(function ($item) use ($course) {
                return $item['level_id'] == $course->level_id && $item['semester_id'] == $course->semester_id;
            });

            if (! $combo) {
                return false;
            }

            $registration = $record->registrations->first(function ($reg) use ($combo) {
                return $reg->level_id == $combo['level_id'] &&
                    $reg->semester_id == $combo['semester_id'] &&
                    $reg->school_session_id == $combo['school_session_id'];
            });

            if (! $registration) {
                return false;
            }

            $totalScore = TotalScore::where([
                'registration_id' => $registration->id,
                'course_id' => $course->id,
            ])->value('total');

            return ($totalScore ?? -1) <= $failedScore;
        });

        return $failedCourses;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStudents::route('/'),
            'create' => Pages\CreateStudent::route('/create'),
            'view' => Pages\ViewStudent::route('/{record}'),
            'edit' => Pages\EditStudent::route('/{record}/edit'),
        ];
    }
}
