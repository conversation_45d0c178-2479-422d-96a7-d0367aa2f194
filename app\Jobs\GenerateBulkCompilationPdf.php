<?php

namespace App\Jobs;

use App\Models\User;
use App\Settings\CollegeSettings;
use Filament\Notifications\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use setasign\Fpdi\Fpdi;
use Spatie\Browsershot\Browsershot;

class GenerateBulkCompilationPdf implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 900; // 15 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $compilationsData,
        public string $fileName,
        public int $userId
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $batchSize = 20; // 20 students per PDF
        $batches = array_chunk($this->compilationsData, $batchSize);
        $pdfPaths = [];

        foreach ($batches as $index => $batch) {
            $batchPdfPath = storage_path("app/compilations/{$this->fileName}_batch_{$index}.pdf");

            $html = view('filament.documents.compilation-bulk', [
                'compilations' => $batch,
                'collegeSettings' => app(CollegeSettings::class),
                'isExport' => true,
                'pageTitle' => $this->fileName,
            ])->render();

            $tempHtmlPath = storage_path("app/temp/{$this->fileName}_batch_{$index}.html");
            if (! file_exists(dirname($tempHtmlPath))) {
                mkdir(dirname($tempHtmlPath), 0777, true);
            }
            file_put_contents($tempHtmlPath, $html);

            Browsershot::htmlFromFilePath($tempHtmlPath)
                ->setChromePath('/usr/bin/chromium-browser')
                ->setNodeBinary('/usr/bin/node')
                ->setNpmBinary('/usr/local/bin/npm')
                ->showBackground()
                ->noSandbox()
                ->timeout(600)
                ->setOption('args', [
                    '--disable-dev-shm-usage',
                    '--no-sandbox',
                    '--disable-gpu',
                ])
                ->savePdf($batchPdfPath);

            @unlink($tempHtmlPath);
            $pdfPaths[] = $batchPdfPath;

            // Free memory between batches
            gc_collect_cycles();
        }

        // Merge PDFs
        $finalPdfPath = storage_path("app/compilations/{$this->fileName}.pdf");
        $this->mergePdfs($pdfPaths, $finalPdfPath);

        // Cleanup batch files
        foreach ($pdfPaths as $path) {
            @unlink($path);
        }

        Notification::make()
            ->title('PDF Ready')
            ->body('Your compilation PDF is ready for download.')
            ->success()
            ->actions([
                Action::make('download')
                    ->label('Download Now')
                    ->url(route('compilation.download-file', ['fileName' => $this->fileName]))
                    ->openUrlInNewTab(),
            ])
            ->sendToDatabase(User::find($this->userId));
    }

    private function mergePdfs(array $pdfPaths, string $outputPath)
    {
        $pdf = new Fpdi;

        foreach ($pdfPaths as $file) {
            $pageCount = $pdf->setSourceFile($file);
            for ($i = 1; $i <= $pageCount; $i++) {
                $pdf->AddPage();
                $tplIdx = $pdf->importPage($i);
                $pdf->useTemplate($tplIdx);
            }
        }

        file_put_contents($outputPath, $pdf->Output('S'));
    }
}
