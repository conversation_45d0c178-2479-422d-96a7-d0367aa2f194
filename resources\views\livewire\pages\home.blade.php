@php
use App\Settings\CollegeSettings;
@endphp

<x-slot name="title">{{ __('Home - RACOED') }}</x-slot>
<div>
    {{-- HERO --}}
    <section class="relative bg-white text-center min-h-screen flex items-center justify-center px-4 py-12">
        <img src="/images/campus-gate.jpg" alt="Campus Gate"
            class="absolute inset-0 w-full h-full object-cover opacity-20 pointer-events-none">
        <div class="relative z-10 bg-white/60 p-6 rounded-sm">
            <h1 class="text-3xl sm:text-4xl font-bold mb-4">{{ app(CollegeSettings::class)->name }}</h1>
            <p class="text-lg mb-6">A learning community where young achievers are prepared for success.</p>
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-center text-center">
                <x-filament::dropdown>
                    <x-slot name="trigger">
                        <x-filament::button icon="heroicon-o-academic-cap" color="primary">
                            APPLY NOW!
                        </x-filament::button>
                    </x-slot>
                    <x-filament::dropdown.list>
                        <x-filament::dropdown.list.item :href="route('nce-full-time')" tag="a" wire:navigate>
                            NCE - Full Time
                        </x-filament::dropdown.list.item>
                    </x-filament::dropdown.list>
                </x-filament::dropdown>

                <x-filament::button color="primary" outlined tag="a" href="https://wa.link/f7fqb2" target="_blank">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                        viewBox="0 0 16 16" class="inline-block mr-1 mb-0.5">
                        <path
                            d="M13.601 2.326A7.854 7.854 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.933 7.933 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.898 7.898 0 0 0 13.6 2.326zM7.994 14.521a6.573 6.573 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.557 6.557 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592zm3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.729.729 0 0 0-.529.247c-.182.198-.691.677-.691 1.654 0 .977.71 1.916.81 2.049.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 1.246.08.38-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232z" />
                    </svg>
                    MAKE ENQUIRY
                </x-filament::button>
            </div>
        </div>
    </section>

    {{-- ABOUT US --}}
    <section class="bg-white py-10">
        <div class="max-w-full sm:max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h2 class="text-2xl font-semibold text-[#96281B] mb-3">About the College</h2>
                <p class="text-gray-700 mb-4">Raphat College of Education is a leading institution of higher learning
                    located in Obaagun, Osun State, Nigeria. The college was established in 2020 with a vision to
                    provide quality education and training for individuals who aspire to become educators and teachers
                    in Nigeria.
                    <br><br>
                    At Raphat College of Education, the focus is on providing students with a well-rounded education
                    that prepares them for success in their chosen careers. The college offers a wide range of academic
                    programs, including the certificate programs in education, as well as professional development
                    courses for teachers…
                </p>
                <a href="/about" class="text-[#96281B] font-semibold underline">Read more</a>
            </div>
        </div>
    </section>

    {{-- PROVOST'S MESSAGE --}}
    <section class="py-12">
        <div class="max-w-full sm:max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <img src="/images/provost.jpg" alt="Provost Photo"
                    class="w-32 h-32 mx-auto rounded-full mb-4 object-cover shadow-md">
                <h2 class="text-2xl font-semibold text-[#96281B] mb-4">Provost’s Welcome Address</h2>
                <p class="text-gray-700 leading-relaxed">
                    Welcome to Raphat College of Education. We are committed to raising teachers of excellence,
                    character, and purpose.
                    Here, you will find a supportive environment to grow academically and professionally. I invite you
                    to explore our college,
                    engage with our staff, and be part of a legacy that transforms lives through education.
                </p>
                <p class="mt-4 font-semibold text-[#96281B]">– Dr. Bolarinwa R.O. (B.Sc., M.P.H, PhD), Provost</p>
            </div>
        </div>
    </section>

    {{-- WHY STUDY AT RACOED --}}
    <section class="bg-white py-12">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-semibold text-[#96281B] mb-8 text-center">Why Study at RACOED?</h2>

            <div class="grid md:grid-cols-3 gap-6 text-gray-700">
                <div class="bg-white p-6 rounded-lg shadow text-center">
                    <img src="/images/specialized-education.jpg" alt="Specialized Education"
                        class="h-24 mx-auto mb-4 rounded">
                    <h3 class="text-lg font-bold mb-2">Specialized Education</h3>
                    <p>Our programmes are tailored to build strong teaching skills and deep subject knowledge, perfect
                        for anyone aiming to thrive in the education sector.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow text-center">
                    <img src="/images/professional-development.jpg" alt="Professional Development"
                        class="h-24 mx-auto mb-4 rounded">
                    <h3 class="text-lg font-bold mb-2">Professional Development</h3>
                    <p>Studying at RACOED opens doors to workshops, trainings, and more opportunities to grow as an
                        educator and advance your career.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow text-center">
                    <img src="/images/quality-certification.jpg" alt="Quality Certification"
                        class="h-24 mx-auto mb-4 rounded">
                    <h3 class="text-lg font-bold mb-2">Quality Certification</h3>
                    <p>We award the National Certificate in Education (NCE), a key requirement to teach in Nigeria. Our
                        graduates are well-positioned for employment.</p>
                </div>
            </div>
        </div>
    </section>

    {{-- NCCE ACCREDITED DEPARTMENTS --}}
    <section class="py-12">
        <div class="max-w-full sm:max-w-3xl md:max-w-5xl lg:max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-[#96281B] text-center mb-10">NCCE Accredited Departments</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
                <div class="bg-white p-6 rounded-sm shadow text-center">
                    <x-filament::icon icon="heroicon-o-globe-europe-africa"
                        class="h-10 w-10 text-[#96281B] mx-auto mb-3" />
                    <h3 class="text-lg font-semibold mb-2">
                        <a href="{{ route('schools.arts') }}" class="hover:underline">Arts & Social Sciences</a>
                    </h3>
                    <ul class="text-gray-700 text-sm space-y-1">
                        <li><a href="{{ route('departments.economics') }}" class="hover:underline">Economics</a></li>
                        <li><a href="{{ route('departments.islamic') }}" class="hover:underline">Islamic Studies</a>
                        </li>
                        <li><a href="{{ route('departments.political') }}" class="hover:underline">Political Science</a>
                        </li>
                        <li><a href="{{ route('departments.social') }}" class="hover:underline">Social Studies</a></li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-sm shadow text-center">
                    <x-filament::icon icon="heroicon-o-language" class="h-10 w-10 text-[#96281B] mx-auto mb-3" />
                    <h3 class="text-lg font-semibold mb-2">
                        <a href="{{ route('schools.languages') }}" class="hover:underline">Languages</a>
                    </h3>
                    <ul class="text-gray-700 text-sm space-y-1">
                        <li><a href="{{ route('departments.arabic') }}" class="hover:underline">Arabic</a></li>
                        <li><a href="{{ route('departments.english') }}" class="hover:underline">English</a></li>
                        <li><a href="{{ route('departments.french') }}" class="hover:underline">French</a></li>
                        <li><a href="{{ route('departments.yoruba') }}" class="hover:underline">Yoruba</a></li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-sm shadow text-center">
                    <x-filament::icon icon="heroicon-o-beaker" class="h-10 w-10 text-[#96281B] mx-auto mb-3" />
                    <h3 class="text-lg font-semibold mb-2">
                        <a href="{{ route('schools.sciences') }}" class="hover:underline">Sciences</a>
                    </h3>
                    <ul class="text-gray-700 text-sm space-y-1">
                        <li><a href="{{ route('departments.biology') }}" class="hover:underline">Biology</a></li>
                        <li><a href="{{ route('departments.chemistry') }}" class="hover:underline">Chemistry</a></li>
                        <li><a href="{{ route('departments.computer') }}" class="hover:underline">Computer Science</a>
                        </li>
                        <li><a href="{{ route('departments.mathematics') }}" class="hover:underline">Mathematics</a>
                        </li>
                        <li><a href="{{ route('departments.physics') }}" class="hover:underline">Physics</a></li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-sm shadow text-center">
                    <x-filament::icon icon="heroicon-o-cog" class="h-10 w-10 text-[#96281B] mx-auto mb-3" />
                    <h3 class="text-lg font-semibold mb-2">
                        <a href="{{ route('schools.vocational') }}" class="hover:underline">Vocational & Technical</a>
                    </h3>
                    <ul class="text-gray-700 text-sm space-y-1">
                        <li><a href="{{ route('departments.business') }}" class="hover:underline">Business Education</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    {{-- CAMPUS NEWS --}}
    <section class="bg-white py-10">
        <div class="max-w-full sm:max-w-3xl md:max-w-5xl lg:max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-semibold text-[#96281B] mb-6 text-center">Campus News</h2>
            @if ($news->count())
            <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                @foreach ($news as $item)
                <div class="bg-gray-100 rounded-sm shadow p-4">
                    <img src="{{ Storage::url($item->image) }}" alt="{{ $item->title }}"
                        class="w-full h-40 object-cover rounded mb-3">
                    <h3 class="text-lg font-semibold text-[#96281B]">{{ $item->title }}</h3>
                    <p class="text-sm text-gray-600 mb-2">{{ \Carbon\Carbon::parse($item->date)->toFormattedDateString()
                        }}</p>
                    <p class="text-gray-700 text-sm line-clamp-3">{!!
                        str($item->content)->markdown()->sanitizeHtml()->limit(100) !!}</p>
                    <a href="{{ route('news.show', $item->slug) }}"
                        class="text-[#96281B] text-sm font-medium mt-2 inline-block">Read More →</a>
                </div>
                @endforeach
            </div>
            <div class="mt-8 text-center">
                <x-filament::button color="primary" outlined tag="a" href="{{ route('news.index') }}" wire:navigate>
                    Load More News
                </x-filament::button>
            </div>
            @else
            <p class="text-center text-gray-600">No campus news available at the moment.</p>
            @endif
        </div>
    </section>

    {{-- WHAT OUR STUDENTS SAY --}}
    <section class="py-12">
        <div class="max-w-full sm:max-w-3xl md:max-w-5xl lg:max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h2 class="text-2xl font-semibold text-[#96281B] mb-6">What Our Students Say</h2>
                <div class="bg-white p-6 rounded shadow text-gray-700 italic max-w-md mx-auto">"RACOED gave me the
                    confidence and skills I needed to succeed in teaching. I’m proud to be an alumna!" – Aisha M.</div>
            </div>
        </div>
    </section>

    {{-- FAQ SECTION --}}
    <section class="bg-white py-12" x-data="{ selected: null }">
        <div class="max-w-full sm:max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-semibold text-[#96281B] mb-6 text-center">Frequently Asked Questions</h2>
            <div class="space-y-4 max-w-3xl mx-auto text-gray-700">
                <!-- Question 1 -->
                <div class="border border-gray-200 rounded">
                    <button class="w-full px-4 py-3 text-left font-semibold focus:outline-none"
                        @click="selected === 1 ? selected = null : selected = 1">
                        Is Raphat College of Education tuition free?
                    </button>
                    <div x-show="selected === 1" x-collapse class="px-4 pb-4">
                        Yes it is. The tuition and application form of the school is free, however, students are
                        required to pay their registration fee once given admission.
                    </div>
                </div>

                <!-- Question 2 -->
                <div class="border border-gray-200 rounded">
                    <button class="w-full px-4 py-3 text-left font-semibold focus:outline-none"
                        @click="selected === 2 ? selected = null : selected = 2">
                        How do I apply for Raphat College of Education?
                    </button>
                    <div x-show="selected === 2" x-collapse class="px-4 pb-4">
                        Applicants should obtain the school form from the school or designated pick-up centres. Fill the
                        form appropriately, attach the required credentials and passport photograph. Submit to the
                        centre in which the form is obtained.
                    </div>
                </div>

                <!-- Question 3 -->
                <div class="border border-gray-200 rounded">
                    <button class="w-full px-4 py-3 text-left font-semibold focus:outline-none"
                        @click="selected === 3 ? selected = null : selected = 3">
                        What are the admission requirements?
                    </button>
                    <div x-show="selected === 3" x-collapse class="px-4 pb-4">
                        Must have completed his/her O’level program before application. Applicants must have 5 credits
                        pass in their O’level result including English language and Mathematics in not more than two
                        sittings. Students with WAEC, NECO, NABTEB, GCE results and their equivalents can apply.
                        Applicants with awaiting result can also apply.
                    </div>
                </div>
            </div>
        </div>
    </section>

    {{-- QUICK LINKS --}}
    <section class="py-12">
        <div class="max-w-full sm:max-w-3xl md:max-w-5xl lg:max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-semibold text-[#96281B] text-center mb-6">Quick Links</h2>
            <div class="grid gap-6 sm:grid-cols-2 md:grid-cols-3 text-center">
                @auth
                <x-filament::button icon="heroicon-o-arrow-long-right" outlined color="primary" tag="a"
                    :href="config('custom.portal_url')">
                    Portal
                </x-filament::button>
                @else
                <x-filament::button icon="heroicon-o-arrow-long-right" outlined color="primary" tag="a"
                    :href="config('custom.portal_url')">
                    Login
                </x-filament::button>
                @endauth

                <x-filament::button icon="heroicon-o-document-text" color="primary" outlined tag="a"
                    :href="route('result-checker')">
                    Results Checker
                </x-filament::button>

                <x-filament::button icon="heroicon-o-phone" color="primary" outlined x-data=""
                    x-on:click="$dispatch('open-modal', { id: 'ict-helpdesk' })">
                    ICT/Helpdesk
                </x-filament::button>

                <x-filament::modal id="ict-helpdesk" width="md" icon="heroicon-o-phone" alignment="center">
                    <x-slot name="heading">
                        Need Help?
                    </x-slot>

                    <x-slot name="description">
                        Reach out to the ICT/Helpdesk via WhatsApp or Email. We're always here.
                    </x-slot>

                    <div class="space-y-4">
                        <p><strong>WhatsApp:</strong> <a
                                href="https://wa.me/{{ app(CollegeSettings::class)->getFormattedIctPhone() ?? config('custom.ict_phone') }}"
                                target="_blank" class="text-primary-600 underline">{{
                                app(CollegeSettings::class)->getFormattedIctPhone() ?? config('custom.ict_phone') }}</a>
                        </p>
                        <p><strong>Email:</strong> <a
                                href="mailto:{{ app(CollegeSettings::class)->ict_email ?? config('custom.ict_email') }}"
                                class="text-primary-600 underline">{{ app(CollegeSettings::class)->ict_email ??
                                config('custom.ict_email') }}</a></p>
                        <p><strong>Office Hours:</strong> Mon-Fri, 8:00 AM - 5:00 PM (WAT)</p>
                    </div>
                </x-filament::modal>
            </div>
        </div>
    </section>
</div>