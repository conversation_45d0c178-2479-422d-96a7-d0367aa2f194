@php
use App\Filament\Student\Resources\ResultResource;
use App\Models\Assessment;
use App\Enums\InvoiceStatus;

$student = $getRecord();

$hasUnpaidPortalFee = $student->hasUnpaidPortalFee();

$assessmentNames = Assessment::pluck('max_score', 'name')->all();

$courseData = ResultResource::getSemesterCourseData($this, $student) ?? [];

// Semester calculations
$semesterTotalCreditUnit = ResultResource::getSemesterTotalCreditUnit($courseData);
$semesterTotalGradePoint = ResultResource::getSemesterTotalGradePoint($courseData);
$semesterGradePointAverage = $semesterTotalCreditUnit > 0 ? number_format($semesterTotalGradePoint /
$semesterTotalCreditUnit, 2) : null;
$semesterGradeRemark = ResultResource::getRemarkFromGradePointAverage($semesterGradePointAverage);
$semesterOutstandingCourses = ResultResource::getSemesterOutstandingCourses($courseData);

// Cumulative calculations
$cumulativeTotalCreditUnit = ResultResource::getCumulativeTotalCreditUnit($this, $student);
$cumulativeTotalGradePoint = ResultResource::getCumulativeTotalGradePoint($this, $student);
$cumulativeGradePointAverage = $cumulativeTotalCreditUnit > 0 ? number_format($cumulativeTotalGradePoint /
$cumulativeTotalCreditUnit, 2) : null;
$cumulativeGradeRemark = ResultResource::getRemarkFromGradePointAverage($cumulativeGradePointAverage);
$cumulativeOutstandingCourses = ResultResource::getCumulativeOutstandingCourses($this, $student);
@endphp

<x-result :assessmentNames="$assessmentNames" :courseData="$courseData"
    :semesterTotalCreditUnit="$semesterTotalCreditUnit" :semesterTotalGradePoint="$semesterTotalGradePoint"
    :semesterGradePointAverage="$semesterGradePointAverage" :semesterGradeRemark="$semesterGradeRemark"
    :semesterOutstandingCourses="$semesterOutstandingCourses" :cumulativeTotalCreditUnit="$cumulativeTotalCreditUnit"
    :cumulativeTotalGradePoint="$cumulativeTotalGradePoint" :cumulativeGradePointAverage="$cumulativeGradePointAverage"
    :cumulativeGradeRemark="$cumulativeGradeRemark" :cumulativeOutstandingCourses="$cumulativeOutstandingCourses"
    :hasUnpaidPortalFee="$hasUnpaidPortalFee" :isExport="false" />