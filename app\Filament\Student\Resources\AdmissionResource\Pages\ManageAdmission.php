<?php

namespace App\Filament\Student\Resources\AdmissionResource\Pages;

use App\Filament\Student\Resources\AdmissionResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;

class ManageAdmission extends ManageRecords
{
    protected static string $resource = AdmissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function printAdmissionLetter($studentId)
    {
        try {
            $url = URL::signedRoute('admission-letter.print', ['student' => $studentId]);

            // If this is a Livewire component
            return $this->js("(function() {
                const newWindow = window.open(
                    '$url',
                    'AdmissionLetter',
                    'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                );
            
                if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                    alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                } else {
                    newWindow.focus();
                }
            })();");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Admission letter print failed:', ['error' => $e->getMessage()]);
            Notification::make()
                ->title('Print Failed')
                ->body('Unable to print admission letter. Please try again later.')
                ->danger()
                ->send();
        }
    }

    public function downloadAdmissionLetter($studentId)
    {
        try {
            $url = URL::signedRoute('admission-letter.download', ['student' => $studentId]);

            // If this is a Livewire component
            return $this->js("window.location.href = '$url';");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Admission letter download failed:', ['error' => $e->getMessage()]);
            Notification::make()
                ->title('Download Failed')
                ->body('Unable to download admission letter. Please try again later.')
                ->danger()
                ->send();
        }
    }

    public function printAcceptanceLetter($studentId)
    {
        try {
            $url = URL::signedRoute('acceptance-letter.print', ['student' => $studentId]);

            // If this is a Livewire component
            return $this->js("(function() {
                const newWindow = window.open(
                    '$url',
                    'AcceptanceLetter',
                    'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                );
            
                if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                    alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                } else {
                    newWindow.focus();
                }
            })();");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Acceptance letter print failed:', ['error' => $e->getMessage()]);
            Notification::make()
                ->title('Print Failed')
                ->body('Unable to print acceptance letter. Please try again later.')
                ->danger()
                ->send();
        }
    }

    public function downloadAcceptanceLetter($studentId)
    {
        try {
            $url = URL::signedRoute('acceptance-letter.download', ['student' => $studentId]);

            // If this is a Livewire component
            return $this->js("window.location.href = '$url';");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Acceptance letter download failed:', ['error' => $e->getMessage()]);
            Notification::make()
                ->title('Download Failed')
                ->body('Unable to download acceptance letter. Please try again later.')
                ->danger()
                ->send();
        }
    }

    public function printApplicationData($studentId)
    {
        try {
            $url = URL::signedRoute('application-data.print', ['student' => $studentId]);

            // If this is a Livewire component
            return $this->js("(function() {
                const newWindow = window.open(
                    '$url',
                    'ApplicationData',
                    'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                );
            
                if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                    alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                } else {
                    newWindow.focus();
                }
            })();");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Application data print failed:', ['error' => $e->getMessage()]);
            Notification::make()
                ->title('Print Failed')
                ->body('Unable to print application data. Please try again later.')
                ->danger()
                ->send();
        }
    }

    public function downloadApplicationData($studentId)
    {
        try {
            $url = URL::signedRoute('application-data.download', ['student' => $studentId]);

            // If this is a Livewire component
            return $this->js("window.location.href = '$url';");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Application data download failed:', ['error' => $e->getMessage()]);
            Notification::make()
                ->title('Download Failed')
                ->body('Unable to download application data. Please try again later.')
                ->danger()
                ->send();
        }
    }
}
