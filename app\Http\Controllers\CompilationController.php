<?php

namespace App\Http\Controllers;

use App\Jobs\GenerateBulkCompilationPdf;
use App\Settings\CollegeSettings;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Spatie\Browsershot\Browsershot;
use Spa<PERSON>\LaravelPdf\Facades\Pdf;

class CompilationController extends Controller
{
    public function print($compilationCacheKey)
    {
        $compilationsData = $this->getCompilationsData($compilationCacheKey);

        return view('filament.documents.compilation-bulk', [
            'compilations' => $compilationsData,
            'collegeSettings' => app(CollegeSettings::class),
            'isExport' => true,
            'pageTitle' => count($compilationsData) > 1
                ? 'Compilations - '.count($compilationsData).' students'
                : 'Compilation - '.($compilationsData[0]['student']['name'] ?? 'Compilation'),
        ]);
    }

    public function download($compilationCacheKey)
    {
        $compilationsData = $this->getCompilationsData($compilationCacheKey);

        // Direct download for small batches
        if (count($compilationsData) <= 20) {
            return $this->downloadDirect($compilationsData);
        }

        // Queue for large batches
        $fileName = 'Compilations_'.now()->format('Y-m-d_His').'_'.count($compilationsData).'_students';

        GenerateBulkCompilationPdf::dispatch(
            $compilationsData,
            $fileName,
            Auth::user()->id,
        );

        return back()->with('filament.notifications', [
            [
                'title' => 'Processing...',
                'body' => 'Generating PDF for '.count($compilationsData).' students. You will be notified when ready (2-5 mins).',
                'status' => 'info',
            ],
        ]);
    }

    private function downloadDirect($compilationsData)
    {
        $fileName = count($compilationsData) > 1
            ? 'Compilations - '.count($compilationsData).' students'
            : 'Compilation - '.($compilationsData[0]['student']['name'] ?? 'Compilation');

        return Pdf::view('filament.documents.compilation-bulk', [
            'compilations' => $compilationsData,
            'collegeSettings' => app(CollegeSettings::class),
            'isExport' => true,
            'pageTitle' => $fileName,
        ])
            ->withBrowsershot(fn (Browsershot $browsershot) => $browsershot
                ->noSandbox()
                ->timeout(120)
                ->setOption('args', ['--disable-dev-shm-usage', '--no-sandbox'])
            )
            ->name($fileName.'.pdf')
            ->download();
    }

    private function getCompilationsData($compilationCacheKey)
    {
        $compilationsData = Cache::get($compilationCacheKey);

        if (! $compilationsData) {
            abort(419, 'Page expired. Please regenerate the compilation.');
        }

        // Handle both single and bulk compilations
        if (isset($compilationsData['student'])) {
            // Single compilation - wrap in array
            return [$compilationsData];
        }

        // Bulk compilations - already an array
        return $compilationsData;
    }

    public function downloadFile($fileName)
    {
        $path = storage_path("app/compilations/{$fileName}.pdf");

        if (! file_exists($path)) {
            abort(404, 'File not found or expired');
        }

        return response()->download($path, $fileName.'.pdf')->deleteFileAfterSend();
    }
}
