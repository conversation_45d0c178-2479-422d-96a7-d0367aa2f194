<?php

namespace App\Filament\Student\Clusters\Cpay\Resources\FeeResource\Pages;

use App\Casts\MoneyCast;
use App\Enums\PaymentMethod;
use App\Filament\Student\Clusters\Cpay\Resources\FeeResource;
use App\Models\Fee;
use App\Models\Transaction;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\HtmlString;
use Livewire\Attributes\On;

class ManageFees extends ManageRecords
{
    protected static string $resource = FeeResource::class;

    protected $listeners = [
        'refresh-table' => '$refresh',
    ];

    public function confirmPaymentAction(): Action
    {
        $activeSchoolSession = activeSchoolSession();

        return Action::make('confirmPayment')
            ->requiresConfirmation()
            ->modalHeading('Confirm Payment')
            ->modalDescription(function (array $arguments) {
                $tableRecord = $arguments['tableRecord'] ?? [];
                $amount = (new MoneyCast)->get(null, 'amount', $arguments['paymentData']['amount'], []);

                return new HtmlString('You are paying a total of <b>₦'.number_format($amount).'</b> for <b>'.$tableRecord['feeCategoryName'].'</b>. <br> 
                Do you wish to proceed?');
            })
            ->modalSubmitActionLabel('Yes, proceed')
            ->modalCancelActionLabel('Cancel')
            ->action(function ($livewire, array $arguments) use ($activeSchoolSession) {
                $paymentData = $arguments['paymentData'] ?? [];

                $tableRecord = $arguments['tableRecord'] ?? [];

                $schoolId = Filament::getTenant()->id;

                $fee = Fee::findOrFail($tableRecord['id']);

                if (PaymentMethod::from($paymentData['method']) === PaymentMethod::ONLINE) {

                    // $paystackReference = (new InvoiceService())->generateTransactionNumber($schoolId);
                    $PaystackSecretKey = config('custom.secret_key');
                    $paystackAmount = $paymentData['amount'];

                    Transaction::create([
                        'fee_id' => $fee->id,
                        'user_id' => $tableRecord['user']['id'],
                        'name' => $tableRecord['user']['name'],
                        'email' => $tableRecord['user']['email'],
                        'amount' => (new MoneyCast)->get(null, 'amount', $paymentData['amount'], []),
                        // 'invoice_number' =>  $paystackReference,
                        'school_id' => $schoolId,
                        'school_session_id' => $activeSchoolSession->id,
                        'payment_category' => $fee->feeCategory?->payment_category,
                        'payment_method' => $paymentData['method'],
                    ]);

                    $paymentData = [
                        'email' => $tableRecord['user']['email'],
                        'amount' => $paystackAmount,
                        // 'reference' =>  $paystackReference,
                        'currency' => 'NGN',
                    ];

                    $paystackTransactionInitializeUrl = config('custom.api_url').'/transaction/initialize';

                    try {
                        $response = Http::withHeaders([
                            'Authorization' => 'Bearer '.$PaystackSecretKey,
                            'Content-Type' => 'application/json',
                        ])->post($paystackTransactionInitializeUrl, $paymentData);

                        $response = json_decode($response->body(), true);

                        if ($response['status'] && isset($response['data']['access_code'])) {

                            $livewire->dispatch('fee-paystack-popup', accessCode: $response['data']['access_code']);
                        } else {
                            throw new \Exception('Invalid Paystack response structure.');
                        }
                    } catch (\Exception $e) {
                        Log::error('Paystack Payment Initiation Failed', [
                            'error' => $e->getMessage(),
                            'response' => isset($response) ? $response : 'No response received',
                            'record' => $tableRecord,
                        ]);

                        Notification::make()
                            ->danger()
                            ->title('Payment Initiation Failed')
                            ->body('Unable to process payment. Please try again. If the issue persists, contact the school admin.')
                            ->persistent()
                            ->send();
                    }
                }
            });
    }

    #[On('fee-payment-success')]
    public function paymentSuccessful()
    {
        Notification::make()
            ->success()
            ->title('Payment completed')
            ->body('The payment was processed successfully. Thank you.')
            ->send();

        $this->dispatch('refresh-table');
    }

    #[On('fee-payment-canceled')]
    public function paymentCanceled()
    {
        Notification::make()
            ->danger()
            ->title('Payment canceled')
            ->body('The payment must be completed to process your fees. Please try again or contact the school admin for assistance.')
            ->send();
    }

    public function printInvoice($transactionId, $due)
    {
        try {
            $url = URL::signedRoute('invoice.print', ['transaction' => $transactionId, 'due' => $due]);

            // If this is a Livewire component
            return $this->js("(function() {
                const newWindow = window.open(
                    '$url',
                    'invoice',
                    'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                );
            
                if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                    alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                } else {
                    newWindow.focus();
                }
            })();");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Invoice print failed:', ['error' => $e->getMessage()]);

            Notification::make()
                ->title('Print Failed')
                ->body('Unable to print invoice. Please try again later.')
                ->danger()
                ->send();
        }
    }

    public function downloadInvoice($transactionId, $due)
    {
        try {
            $url = URL::signedRoute('invoice.download', ['transaction' => $transactionId, 'due' => $due]);

            // If this is a Livewire component
            return $this->js("window.location.href = '$url';");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Invoice download failed:', ['error' => $e->getMessage()]);

            Notification::make()
                ->title('Download Failed')
                ->body('Unable to download invoice. Please try again later.')
                ->danger()
                ->send();
        }
    }
}
