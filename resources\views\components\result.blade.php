@php
use App\Filament\Student\Resources\ResultResource;
use App\Models\Assessment;
@endphp

<div class="my-3 space-y-3">

    <!-- Result Sheet -->
    <div class="border border-gray-300 rounded-sm overflow-hidden">
        <div class="text-center {{ $isExport ? 'px-20 py-3' : 'px-3 py-3' }}">
            <h2 class="font-bold mb-2">Result Sheet</h2>

            <table class="w-full table-auto text-sm border border-gray-300">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-2 py-1">#</th>
                        <th class="border px-2 py-1">Course</th>
                        @foreach ($assessmentNames as $assessment => $max_score)
                        <th class="border px-2 py-1 text-center">
                            {{ $assessment }}
                            <span class="block text-xs">({{ $max_score }})</span>
                        </th>
                        @endforeach
                        <th class="border px-2 py-1 text-center">
                            Total <span class="block text-xs">({{ collect($assessmentNames)->sum() }})</span>
                        </th>
                        <th class="border px-2 py-1 text-center">Grade</th>
                        <th class="border px-2 py-1 text-center">Point</th>
                        <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point">GP</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($courseData as $index => $data)
                    @php $failed = ($data['total_score'] ?? -1) <= ResultResource::getFailedScore(); @endphp <tr>
                        <td class="border px-2 py-1 text-center">{{ $index + 1 }}</td>
                        <td class="border px-2 py-1" x-tooltip.raw="{{ $data['title'] }}">
                            {{ $data['code'] }}
                            <span class="text-gray-800"> {{ $data['credit'] }}{{ $data['status'] }}</span>
                        </td>
                        @foreach ($assessmentNames as $assessment => $max_score)
                        <td class="border px-2 py-1 text-center">{{ $data[$assessment] ?? '-' }}</td>
                        @endforeach
                        <td class="border px-2 py-1 text-center text-{{ $failed ? 'red-500' : 'gray-900'}}">
                            {{ $data['total_score'] ?? '-' }}
                        </td>
                        <td class="border px-2 py-1 text-center text-{{ $failed ? 'red-500' : 'gray-900' }}">
                            {{ $data['grade'] ?? '-' }}
                        </td>
                        <td class="border px-2 py-1 text-center">{{ $data['point'] ?? '-' }}</td>
                        <td class="border px-2 py-1 text-center">{{ $data['grade_point'] ?? '-' }}</td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="{{ 6 + count($assessmentNames) }}" class="text-center py-2">No scores found.
                            </td>
                        </tr>
                        @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Result Summary -->
    <div class="border border-gray-300 rounded-sm overflow-hidden {{ $isExport ? 'px-20 py-3' : 'px-3 py-3' }}">
        <h2 class="font-bold text-center mb-2">Result Summary</h2>

        <!-- Semester Summary -->
        <div class="mb-3">
            <h3 class="font-bold text-center mb-2">Semester</h3>
            <table class="w-full table-auto border border-gray-300 text-sm text-center">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-3 py-2" x-tooltip.raw="Total Credit Unit">TCU</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Total Grade Point">TGP</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Grade Point Average">GPA</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Remark">Remark</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Outstanding Courses">Outstanding</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="border px-3 py-2">{{ $semesterTotalCreditUnit }}</td>
                        <td class="border px-3 py-2">{{ $semesterTotalGradePoint }}</td>
                        <td class="border px-3 py-2">{{ $semesterGradePointAverage ?? '-' }}</td>
                        <td class="border px-3 py-2">{{ $semesterGradeRemark?->remark ?? '-' }}</td>
                        <td class="border px-3 py-2 text-start max-w-[150px]">
                            <div class="overflow-auto max-h-[100px]">
                                {{ $semesterOutstandingCourses->isNotEmpty() ?
                                $semesterOutstandingCourses->pluck('code')->implode(', ') : 'NIL' }}
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Cumulative Summary -->
        <div>
            <h3 class="font-bold text-center mb-2">Cumulative</h3>
            <table class="w-full table-auto border border-gray-300 text-sm text-center">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-3 py-2" x-tooltip.raw="Cumulative Total Credit Unit">CTCU</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Cumulative Total Grade Point">CTGP</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Cumulative Grade Point Average">CGPA</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Cumulative Remark">C. Remark</th>
                        <th class="border px-3 py-2" x-tooltip.raw="Cumulative Outstanding Courses">C. Outstanding</th>
                    </tr>
                </thead>
                <tbody>
                    @if($hasUnpaidPortalFee ?? false)
                    <tr>
                        <td colspan="5" class="border px-3 py-2 text-center">
                            You have unpaid portal fees. Please pay now to get full access.
                        </td>
                    </tr>
                    @else
                    <tr>
                        <td class="border px-3 py-2">{{ $cumulativeTotalCreditUnit }}</td>
                        <td class="border px-3 py-2">{{ $cumulativeTotalGradePoint }}</td>
                        <td class="border px-3 py-2">{{ $cumulativeGradePointAverage ?? '-' }}</td>
                        <td class="border px-3 py-2">{{ $cumulativeGradeRemark?->remark ?? '-' }}</td>
                        <td class="border px-3 py-2 text-start max-w-[150px]">
                            <div class="overflow-auto max-h-[100px]">
                                {{ $cumulativeOutstandingCourses->isNotEmpty() ?
                                $cumulativeOutstandingCourses->pluck('code')->implode(', ') : 'NIL' }}
                            </div>
                        </td>
                    </tr>
                    @endif
                </tbody>
            </table>
        </div>
    </div>

    @if($isExport)
    <!-- Key -->
    <div class="border border-gray-300 rounded-sm overflow-hidden px-3 py-3">
        <h2 class="font-bold text-center mb-2">Key</h2>
        <div class="text-sm text-center">
            <strong>GP</strong> – Grade Point | <strong>TCU</strong> – Total Course Units | <strong>TCP</strong> – Total
            Credit Points | <strong>GPA</strong> – Grade Point Average | <strong>C</strong> – Cumulative
        </div>
    </div>
    @endif

</div>