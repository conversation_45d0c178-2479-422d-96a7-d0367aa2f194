<?php

namespace App\Livewire;

use App\Enums\FeeType;
use App\Enums\InvoiceStatus;
use App\Filament\Components\StudentForm;
use App\Models\Invoice;
use App\Models\User;
use App\Services\CodeGenerationService;
use App\Services\PaystackService;
use App\Settings\PortalSettings;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Livewire\Attributes\On;
use Livewire\Component;

class NceFullTimeApplication extends Component implements HasActions, HasForms
{
    use InteractsWithActions;
    use InteractsWithForms;

    public ?string $activeSchoolSessionName;

    public ?array $formData = [];

    public int $portalFee;

    public function mount(): void
    {
        $this->form->fill();
        $this->activeSchoolSessionName = activeSchoolSession()?->name;
        $this->portalFee = app(PortalSettings::class)->portal_fee;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(
                StudentForm::schema(),
            )
            ->statePath('formData')
            ->model(User::class);
    }

    public function submitAction(): void
    {
        $this->form->validate();
        $this->mountAction('confirmPaymentAction');
    }

    public function confirmPaymentAction(): Action
    {
        return Action::make('confirmPayment')
            ->requiresConfirmation()
            ->modalHeading('Confirm Payment and Application Submission?')
            ->modalDescription(new HtmlString('
                    You are about to pay a non-refundable portal fee of <b>₦'.number_format($this->portalFee).'</b> and submit your NCE Full-Time application.<br>
                    Do you want to proceed?
                '))
            ->modalSubmitActionLabel('Proceed')
            ->mountUsing(fn () => $this->form->validate())
            ->action(fn () => $this->initiatePayment());
    }

    public function initiatePayment()
    {
        try {
            $applicationData = $this->form->getState();

            $item = [
                'item' => 'Portal fee',
                'amount' => $this->portalFee,
            ];

            $invoice = Invoice::create([
                // 'user_id' => null,
                // 'payable_id' => null,
                // 'payable_type' => null,
                'number' => app(CodeGenerationService::class)->generateInvoiceNumber(),
                'reference' => app(CodeGenerationService::class)->generateReference(),
                'description' => [$item],
                'total_amount' => $this->portalFee,
                'invoice_status' => InvoiceStatus::INITIATED,
                'fee_type' => FeeType::PORTAL,
                'metadata' => [
                    'applicationData' => $applicationData,
                ],

            ]);

            $response = (app(PaystackService::class))->initializePayment($applicationData['email'], $this->portalFee, $invoice->reference);
            $responseData = $response->json();

            if ($response->successful() && $responseData && $responseData['status']) {

                $this->dispatch('paystack-popup', accessCode: $responseData['data']['access_code'], invoiceId: $invoice->id);
            } else {
                $errorMessage = $responseData['message'] ?? 'Payment initialization failed with Paystack.';
                throw new \Exception($errorMessage);
            }
        } catch (\Exception $e) {
            Log::error('Paystack payment initiation failed', [
                'error' => $e->getMessage(),
                'response' => isset($response) ? $response : 'No response received',
                'formData' => $applicationData,
            ]);

            Notification::make()
                ->danger()
                ->title('Payment Initiation Failed')
                ->body('Unable to process payment. Please try again. If the issue persists, contact the school admin.')
                ->persistent()
                ->send();
        }
    }

    #[On('payment-success')]
    public function paymentSuccess($invoiceId)
    {
        $invoice = Invoice::withTrashed()->find($invoiceId);

        if ($invoice) {
            $invoice->update(['payment_success' => true]);

            if ($invoice->trashed()) {
                $invoice->restore();
            }
        }

        session()->flash('paid', true);

        return redirect()->route('thank-you');
    }

    #[On('payment-canceled')]
    public function paymentCanceled($invoiceId)
    {
        $invoice = Invoice::find($invoiceId);

        if ($invoice) {
            $invoice->delete();
        }

        Notification::make()
            ->danger()
            ->title('Payment Canceled')
            ->body('Payment must be completed to submit your application. Please try again or contact the school admin if you encounter any issues.')
            ->send();
    }

    public function render(): View
    {
        return view('livewire.pages.nce-full-time-application');
    }
}
