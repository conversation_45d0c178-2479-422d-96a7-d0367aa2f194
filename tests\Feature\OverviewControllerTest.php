<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Support\Facades\URL;

class OverviewControllerTest extends TestCase
{

    public function test_overview_routes_exist()
    {
        // Test that the routes are properly registered
        $this->assertTrue(
            collect(\Illuminate\Support\Facades\Route::getRoutes())
                ->contains(fn($route) => $route->getName() === 'overview.print')
        );

        $this->assertTrue(
            collect(\Illuminate\Support\Facades\Route::getRoutes())
                ->contains(fn($route) => $route->getName() === 'overview.download')
        );
    }

    public function test_overview_controller_exists()
    {
        // Test that the OverviewController class exists
        $this->assertTrue(class_exists(\App\Http\Controllers\OverviewController::class));
    }

    public function test_overview_document_template_exists()
    {
        // Test that the overview document template exists
        $this->assertTrue(view()->exists('filament.documents.overview'));
    }

    public function test_signed_routes_can_be_generated()
    {
        // Test that signed routes can be generated with a dummy cache key
        $printUrl = URL::signedRoute('overview.print', ['overviewCacheKey' => 'test_cache_key']);
        $downloadUrl = URL::signedRoute('overview.download', ['overviewCacheKey' => 'test_cache_key']);

        $this->assertNotEmpty($printUrl);
        $this->assertNotEmpty($downloadUrl);
        $this->assertStringContainsString('overview/print', $printUrl);
        $this->assertStringContainsString('overview/download', $downloadUrl);
    }
}
