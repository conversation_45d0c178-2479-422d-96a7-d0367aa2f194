@php
use App\Filament\Staff\Resources\OverviewResource;
@endphp

<div class="border border-gray-300 rounded-sm overflow-hidden my-3">
    <div class="p-4">
        <h2 class="font-bold text-center mb-2">Result Overview</h2>

        <div class="overflow-x-auto">
            <table class="w-full table-auto text-sm border border-gray-300">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-2 py-1">#</th>
                        <th class="border px-2 py-1">Name</th>
                        <th class="border px-2 py-1">Matric No.</th>

                        @foreach ($courses as $course)
                        <th colspan="4" class="border px-2 py-1 text-center" x-tooltip.raw="{{ $course->title }}">
                            {{ $course->code }}
                            <span class="text-gray-400">
                                {{ $course->credit }}{{ $course->course_status->value }}
                            </span>
                        </th>
                        @endforeach

                        <th colspan="4" class="border px-2 py-1 text-center" x-tooltip.raw="Total Credit Unit">
                            Semester Summary (TCU: {{ $semesterTotalCreditUnit }})
                        </th>
                        <th colspan="4" class="border px-2 py-1 text-center"
                            x-tooltip.raw="Cumulative Total Credit Unit">
                            Cumulative Summary (CTCU: {{ $cumulativeTotalCreditUnit }})
                        </th>
                    </tr>

                    <tr class="bg-gray-50">
                        <th class="border px-2 py-1"></th>
                        <th class="border px-2 py-1"></th>
                        <th class="border px-2 py-1"></th>

                        @foreach ($courses as $course)
                        <th class="border px-2 py-1 text-center" x-tooltip.raw="Total Score">S</th>
                        <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade">G</th>
                        <th class="border px-2 py-1 text-center" x-tooltip.raw="Point">P</th>
                        <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point">GP</th>
                        @endforeach

                        <th class="border px-2 py-1 text-center" x-tooltip.raw="Total Grade Point">TGP</th>
                        <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point Average">GPA</th>
                        <th class="border px-2 py-1 text-center" x-tooltip.raw="Remark">Remark</th>
                        <th class="border px-2 py-1 text-center" x-tooltip.raw="Outstanding Courses">Outstanding</th>
                        <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Total Grade Point">CTGP</th>
                        <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative GPA">CGPA</th>
                        <th class="border px-2 py-1 text-center whitespace-nowrap" x-tooltip.raw="Cumulative Remark">C.
                            Remark</th>
                        <th class="border px-2 py-1 text-center whitespace-nowrap"
                            x-tooltip.raw="Cumulative Outstanding Courses">C. Outstanding</th>
                    </tr>
                </thead>

                <tbody>
                    @forelse ($filteredRecords as $index => $record)
                    @php
                    if ($livewire) {
                    $semesterTotalGradePoint = OverviewResource::getSemesterTotalGradePointWithFilters(
                    OverviewResource::extractFilters($livewire), $record, $courses);
                    $semesterOutstandingCourses = OverviewResource::getSemesterOutstandingCoursesWithFilters(
                    OverviewResource::extractFilters($livewire), $record, $courses);
                    $cumulativeTotalGradePoint = OverviewResource::getCumulativeTotalGradePoint($livewire, $record);
                    $cumulativeOutstandingCourses = OverviewResource::getCumulativeOutstandingCourses($livewire,
                    $record);
                    } else {
                    $semesterTotalGradePoint = OverviewResource::getSemesterTotalGradePointWithFilters($filters,
                    $record, $courses);
                    $semesterOutstandingCourses = OverviewResource::getSemesterOutstandingCoursesWithFilters($filters,
                    $record, $courses);
                    $cumulativeTotalGradePoint = OverviewResource::getCumulativeTotalGradePointWithFilters($filters,
                    $record);
                    $cumulativeOutstandingCourses =
                    OverviewResource::getCumulativeOutstandingCoursesWithFilters($record, $filters);
                    }

                    $gpa = $semesterTotalCreditUnit > 0 ? number_format($semesterTotalGradePoint /
                    $semesterTotalCreditUnit, 2) : 0;
                    $remark = OverviewResource::getRemarkFromGradePointAverage($gpa);
                    $outstandingCourses = $semesterOutstandingCourses->pluck('code')->implode(', ') ?: 'NIL';
                    $cgpa = $cumulativeTotalCreditUnit > 0 ? number_format($cumulativeTotalGradePoint /
                    $cumulativeTotalCreditUnit, 2) : 0;
                    $cumulativeRemark = OverviewResource::getRemarkFromGradePointAverage($cgpa);
                    $cumulativeOutstanding = $cumulativeOutstandingCourses->pluck('code')->implode(', ') ?: 'NIL';
                    @endphp

                    <tr>
                        <td class="border px-2 py-1">{{ $index + 1 }}</td>
                        <td class="border px-2 py-1 whitespace-nowrap">{{ $record->name }}</td>
                        <td class="border px-2 py-1 whitespace-nowrap">{{ $record->matric_number }}</td>

                        @foreach ($courses as $course)
                        @php
                        $courseData = $livewire
                        ? OverviewResource::getCourseData($livewire, $record, $course)
                        : OverviewResource::getCourseDataWithFilters($filters, $record, $course);
                        @endphp
                        <td
                            class="border px-2 py-1 {{ ($courseData['total_score'] ?? -1) <= $failedScore ? 'text-red-600' : '' }}">
                            {{ $courseData['total_score'] ?? '-' }}
                        </td>
                        <td
                            class="border px-2 py-1 {{ ($courseData['total_score'] ?? -1) <= $failedScore ? 'text-red-600' : '' }}">
                            {{ $courseData['grade'] ?? '-' }}
                        </td>
                        <td class="border px-2 py-1">{{ $courseData['point'] ?? '-' }}</td>
                        <td class="border px-2 py-1">{{ $courseData['grade_point'] ?? '-' }}</td>
                        @endforeach

                        <td class="border px-2 py-1">{{ $semesterTotalGradePoint }}</td>
                        <td class="border px-2 py-1">{{ $gpa }}</td>
                        <td class="border px-2 py-1 whitespace-nowrap">{{ $remark?->remark ?? '-' }}</td>
                        <td class="border px-2 py-1">
                            <div class="max-w-[150px] overflow-x-auto {{ $livewire ? 'whitespace-nowrap' : '' }}">
                                {{ $outstandingCourses }}
                            </div>
                        </td>

                        <td class="border px-2 py-1">{{ $cumulativeTotalGradePoint }}</td>
                        <td class="border px-2 py-1">{{ $cgpa }}</td>
                        <td class="border px-2 py-1 whitespace-nowrap">{{ $cumulativeRemark?->remark ?? '-' }}</td>
                        <td class="border px-2 py-1">
                            <div class="max-w-[150px] overflow-x-auto {{ $livewire ? 'whitespace-nowrap' : '' }}">
                                {{ $cumulativeOutstanding }}
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="{{ 11 + ($courses->count() * 4) }}" class="border px-2 py-2 text-center">
                            No students found.
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>