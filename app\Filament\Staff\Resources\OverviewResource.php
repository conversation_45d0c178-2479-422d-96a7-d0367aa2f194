<?php

namespace App\Filament\Staff\Resources;

use App\Enums\AdmissionStatus;
use App\Enums\Role;
use App\Filament\Staff\Resources\OverviewResource\Pages;
use App\Models\Assessment;
use App\Models\Course;
use App\Models\Department;
use App\Models\Grade;
use App\Models\Level;
use App\Models\Registration;
use App\Models\SchoolSession;
use App\Models\Score;
use App\Models\Scoresheet;
use App\Models\Semester;
use App\Models\TotalScore;
use App\Models\User;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Support\Enums\ActionSize;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Columns\ColumnGroup;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class OverviewResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?int $navigationSort = 3;

    protected static ?string $modelLabel = 'Overview';

    protected static ?string $pluralModelLabel = 'Overview';

    protected static ?string $navigationGroup = 'Result';

    public static function canAccess(): bool
    {
        return main_staff_plus_h_o_d_access();
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('role', Role::STUDENT)
            ->whereHas('application', fn ($q) => $q->where('admission_status', AdmissionStatus::APPROVED))
            ->with(['registrations']);
    }

    public static function table(Table $table): Table
    {

        return $table
            ->deferLoading()
            ->deferFilters()
            ->persistFiltersInSession()
            ->paginated([10, 20, 30, 40, 50])
            ->striped()
            ->recordAction(null)
            ->defaultSort('last_name')
            ->emptyStateHeading(fn (HasTable $livewire) => new HtmlString(self::getEmptyStateHeading($livewire)))
            ->emptyStateDescription(fn (HasTable $livewire) => new HtmlString(self::getEmptyStateDescription($livewire)))
            ->description('Overview provides a comprehensive summary of students’ academic performance.')
            ->searchPlaceholder('Search (student name)')

            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('name')
                    ->sortable(
                        query: fn ($query, $direction) => $query->orderBy('users.last_name', $direction)
                    )
                    ->searchable(['last_name', 'first_name', 'middle_name']),
                TextColumn::make('matric_number')
                    ->label('Matric no.'),
                ColumnGroup::make('Semester summary')
                    ->columns([
                        TextColumn::make('gpa')
                            ->label(new HtmlString("<div x-tooltip=\"'Grade Point Average'\">GPA</div>"))
                            ->state(function ($record, $livewire) {
                                $courseData = self::getSemesterCourseData($livewire, $record) ?? [];

                                // Semester calculations
                                $semesterTotalCreditUnit = self::getSemesterTotalCreditUnit($courseData);
                                $semesterTotalGradePoint = self::getSemesterTotalGradePoint($courseData);
                                $semesterGradePointAverage = $semesterTotalCreditUnit > 0 ? number_format($semesterTotalGradePoint / $semesterTotalCreditUnit, 2) : null;
                                $record->semesterGradeRemark = self::getRemarkFromGradePointAverage($semesterGradePointAverage);
                                $record->semesterOutstandingCourses = self::getSemesterOutstandingCourses($courseData);

                                return $semesterGradePointAverage;
                            }),
                        TextColumn::make('remark')
                            ->state(fn ($record) => $record->semesterGradeRemark?->remark),
                        TextColumn::make('outstanding')
                            ->words(4)
                            ->tooltip(function ($state): ?string {
                                $courseCount = count(array_filter(explode(',', $state)));

                                if ($courseCount <= 2) {
                                    return null;
                                }

                                return $state;
                            })
                            ->label(new HtmlString("<div x-tooltip=\"'Carry-over courses'\">Outstanding</div>"))
                            ->state(function ($record) {

                                return $record->semesterOutstandingCourses->pluck('code')->implode(', ');
                            }),
                    ])->alignment(Alignment::Center),
                // ...self::getCourseColumns($livewire),
            ])

            ->filters([
                SelectFilter::make('result_filter')
                    ->form([
                        Select::make('school_session_id')
                            ->required()
                            ->label('Session')
                            ->native(false)
                            ->options(function () {
                                return SchoolSession::query()
                                    ->orderBy('name', 'desc')
                                    ->pluck('name', 'id');
                            })
                            ->default(activeSchoolSession()->id),
                        Select::make('semester_id')
                            ->required()
                            ->label('Semester')
                            ->native(false)
                            ->options(function () {
                                return Semester::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            })
                            ->default(activeSemester()?->id),
                        Select::make('level_id')
                            ->required()
                            ->label('Level')
                            ->native(false)
                            ->options(function () {
                                return Level::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            }),
                        Select::make('department_id')
                            ->required()
                            ->label('Department')
                            ->native(false)
                            ->searchable()
                            ->options(function () {
                                $query = Department::query()->orderBy('name');

                                if (Auth::user()->role === Role::HOD) {
                                    $query->whereIn('id', Auth::user()->departments->pluck('id'));
                                }

                                return $query->pluck('name', 'id');
                            }),
                    ])
                    ->columns(4)
                    ->baseQuery(function (Builder $query, HasTable $livewire): Builder {
                        if (! self::hasRequiredFilters($livewire) || ! self::isScoresheetAvailable($livewire) || ! self::isCourseAvailable($livewire)) {
                            return $query->whereRaw('1 = 0');
                        }

                        return $query;
                    })
                    ->query(function (Builder $query, array $data) {
                        $department = Department::find($data['department_id']);

                        return $query->whereHas('registrations', function ($q) use ($data, $department) {
                            $q->where('school_session_id', $data['school_session_id'])
                                ->where('semester_id', $data['semester_id'])
                                ->where('level_id', $data['level_id']);

                            if (! ($department?->is_edu || $department?->is_gse)) {
                                $q->where(function ($q) use ($data) {
                                    $q->whereHas('programme', function ($q) use ($data) {
                                        $q->where('first_department_id', $data['department_id'])
                                            ->orWhere('second_department_id', $data['department_id']);
                                    });
                                });
                            }
                        });
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($sessionId = $data['school_session_id'] ?? null) {
                            if ($name = SchoolSession::find($sessionId)?->name) {
                                $indicators[] = Indicator::make("Session: {$name}")->removable(false);
                            }
                        }

                        if ($semesterId = $data['semester_id'] ?? null) {
                            if ($name = Semester::find($semesterId)?->name) {
                                $indicators[] = Indicator::make("Semester: {$name}")->removable(false);
                            }
                        }

                        if ($levelId = $data['level_id'] ?? null) {
                            if ($name = Level::find($levelId)?->name) {
                                $indicators[] = Indicator::make("Level: {$name}")->removable(false);
                            }
                        }

                        if ($deptId = $data['department_id'] ?? null) {
                            if ($name = Department::find($deptId)?->name) {
                                $indicators[] = Indicator::make("Department: {$name}")->removable(false);
                            }
                        }

                        return $indicators;
                    }),

            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(1)
            ->filtersApplyAction(
                fn (Action $action) => $action->label('View overview'),
            )
            ->actions([

                ActionGroup::make([
                    Action::make('result')
                        ->visible(fn () => main_staff_access())
                        ->modalSubmitAction(false)
                        ->modalCancelActionLabel('Close')
                        ->modalWidth(MaxWidth::FiveExtraLarge)
                        ->modalContent(function ($record, HasTable $livewire) {
                            $resultData = self::getResultData($livewire, $record);

                            return view('components.result', [
                                'hasUnpaidPortalFee' => false,
                                'isExport' => false,
                                ...$resultData,
                            ]);

                        }),
                    Action::make('compilation')
                        ->visible(fn () => main_staff_plus_h_o_d_access())
                        ->modalSubmitAction(false)
                        ->modalCancelActionLabel('Close')
                        ->modalWidth(MaxWidth::FiveExtraLarge)
                        ->modalContent(function ($record, HasTable $livewire) {
                            $compiledData = self::getCompiledData($livewire, $record);

                            return view('components.compilation', [
                                'isExport' => false,
                                ...$compiledData,
                            ]);

                        }),

                ])
                    ->tooltip('View actions')
                    ->icon('heroicon-o-eye')
                    ->size(ActionSize::Large),

                ActionGroup::make([

                    ActionGroup::make([
                        Action::make('printResult')
                            ->label('Print')
                            ->icon('heroicon-m-printer')
                            ->action(function ($record, $livewire, $action) {

                                if ($record->hasUnpaidPortalFee()) {
                                    Notification::make()
                                        ->title('Print Failed')
                                        ->body('Unable to print result due to unpaid portal fees. The student must clear all outstanding portal fees before printing.')
                                        ->danger()
                                        ->send();

                                    $action->halt();
                                }

                                try {

                                    $resultData = self::getResultData($livewire, $record);

                                    $resultCacheKey = 'resultData_'.uniqid();
                                    Cache::put($resultCacheKey, $resultData, now()->addMinutes(5));

                                    $url = URL::signedRoute('result.print', ['resultCacheKey' => $resultCacheKey]);

                                    $livewire->js("(function() {
                                        const newWindow = window.open(
                                            '$url',
                                            'Result',
                                            'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                                        );
                                        if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                                            alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                                        } else {
                                            newWindow.focus();
                                        }
                                        })();");
                                } catch (\Exception $e) {
                                    Log::error('Result print failed:', ['error' => $e->getMessage()]);
                                    Notification::make()
                                        ->title('Print Error')
                                        ->body('Unable to print result. Please try again later.')
                                        ->danger()
                                        ->send();
                                }
                            }),
                        Action::make('downloadResult')
                            ->label('Download')
                            ->icon('heroicon-m-document-arrow-down')
                            ->action(function ($record, $livewire, $action) {
                                if ($record->hasUnpaidPortalFee()) {
                                    Notification::make()
                                        ->title('Download Failed')
                                        ->body('Unable to download result due to unpaid portal fees. The student must clear all outstanding portal fees before downloading.')
                                        ->danger()
                                        ->send();

                                    $action->halt();
                                }
                                try {
                                    $resultData = self::getResultData($livewire, $record);

                                    $resultCacheKey = 'resultData_'.uniqid();
                                    Cache::put($resultCacheKey, $resultData, now()->addMinutes(5));

                                    $url = URL::signedRoute('result.download', ['resultCacheKey' => $resultCacheKey]);

                                    $livewire->js("window.location.href = '$url';");
                                } catch (\Exception $e) {
                                    Log::error('Result download failed:', ['error' => $e->getMessage()]);
                                    Notification::make()
                                        ->title('Download Error')
                                        ->body('Unable to download result. Please try again later.')
                                        ->danger()
                                        ->send();
                                }
                            }),
                    ])
                        ->visible(fn () => main_staff_access())
                        ->link()
                        ->icon(false)
                        ->label('Result')
                        ->extraAttributes([
                            'style' => 'margin-left: 0.75rem !important; margin-bottom: 0.5rem !important;  margin-top: 0.5rem !important;',
                        ]),
                    ActionGroup::make([
                        Action::make('printCompilation')
                            ->label('Print')
                            ->icon('heroicon-m-printer')
                            ->action(function ($record, $livewire, $action) {
                                try {
                                    $compiledData = self::getCompiledData($livewire, $record);

                                    $compilationCacheKey = 'compilationData_'.uniqid();
                                    Cache::put($compilationCacheKey, $compiledData, now()->addMinutes(5));

                                    $url = URL::signedRoute('compilation.print', ['compilationCacheKey' => $compilationCacheKey]);

                                    $livewire->js("(function() {
                                        const newWindow = window.open(
                                            '$url',
                                            'Compilation',
                                            'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                                        );

                                        if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                                            alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                                        } else {
                                            newWindow.focus();
                                        }
                                    })();");
                                } catch (\Exception $e) {
                                    Log::error('Compilation print failed:', ['error' => $e->getMessage()]);
                                    Notification::make()
                                        ->title('Print Error')
                                        ->body('Unable to print compilation. Please try again later.')
                                        ->danger()
                                        ->send();
                                }
                            }),
                        Action::make('downloadCompilation')
                            ->label('Download')
                            ->icon('heroicon-m-document-arrow-down')
                            ->action(function ($record, $livewire) {
                                try {
                                    $compiledData = self::getCompiledData($livewire, $record);

                                    $compilationCacheKey = 'compilationData_'.uniqid();
                                    Cache::put($compilationCacheKey, $compiledData, now()->addMinutes(5));

                                    $url = URL::signedRoute('compilation.download', ['compilationCacheKey' => $compilationCacheKey]);
                                    $livewire->js("window.location.href = '$url';");
                                } catch (\Exception $e) {
                                    Log::error('Compilation download failed:', ['error' => $e->getMessage()]);
                                    Notification::make()
                                        ->title('Download Error')
                                        ->body('Unable to download compilation. Please try again later.')
                                        ->danger()
                                        ->send();
                                }
                            }),
                    ])
                        ->visible(fn () => main_staff_plus_h_o_d_access())
                        ->link()
                        ->icon(false)
                        ->label('Compilation')
                        ->extraAttributes([
                            'style' => 'margin-left: 0.75rem !important; margin-bottom: 0.5rem !important;',
                        ]),

                    // ActionGroup::make([
                    //     Action::make('printTranscript')
                    //         ->label('Print')
                    //         ->icon('heroicon-m-printer')
                    //         ->action(function ($record, $livewire) {
                    //             try {
                    //                 // Generate signed URL
                    //                 $url = URL::signedRoute('transcript.print', ['student' => $record->id]);

                    //                 $livewire->js("(function() {
                    //                     const newWindow = window.open(
                    //                         '$url',
                    //                         'Transcript',
                    //                         'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                    //                     );

                    //                     if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                    //                         alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                    //                     } else {
                    //                         newWindow.focus();
                    //                     }
                    //                 })();");
                    //             } catch (\Exception $e) {
                    //                 Log::error('Transcript print failed:', ['error' => $e->getMessage()]);
                    //                 Notification::make()
                    //                     ->title('Print Error')
                    //                     ->body('Unable to print transcript. Please try again later.')
                    //                     ->danger()
                    //                     ->send();
                    //             }
                    //         }),
                    //     Action::make('downloadTranscript')
                    //         ->label('Download')
                    //         ->icon('heroicon-m-document-arrow-down')
                    //         ->action(function ($record, $livewire) {
                    //             try {
                    //                 $url = URL::signedRoute('transcript.download', ['student' => $record->id]);
                    //                 $livewire->js("window.location.href = '$url';");
                    //             } catch (\Exception $e) {
                    //                 Log::error('Transcript download failed:', ['error' => $e->getMessage()]);
                    //                 Notification::make()
                    //                     ->title('Download Error')
                    //                     ->body('Unable to download transcript. Please try again later.')
                    //                     ->danger()
                    //                     ->send();
                    //             }
                    //         }),
                    // ])
                    //     ->link()
                    //     ->icon(false)
                    //     ->label('Transcript')
                    //     ->extraAttributes([
                    //         'style' => 'margin-left: 0.75rem !important; margin-bottom: 0.5rem !important;',
                    //     ]),

                    // ActionGroup::make([
                    //     Action::make('printStatement')
                    //         ->label('Print')
                    //         ->icon('heroicon-m-printer')
                    //         ->action(function ($record, $livewire) {
                    //             try {

                    //                 $url = URL::signedRoute('statement.print', ['student' => $record->id]);

                    //                 $livewire->js("(function() {
                    //                     const newWindow = window.open(
                    //                         '$url',
                    //                         'Statement',
                    //                         'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                    //                     );

                    //                     if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                    //                         alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                    //                     } else {
                    //                         newWindow.focus();
                    //                     }
                    //                 })();");
                    //             } catch (\Exception $e) {
                    //                 Log::error('Statement print failed:', ['error' => $e->getMessage()]);
                    //                 Notification::make()
                    //                     ->title('Print Error')
                    //                     ->body('Unable to print statement. Please try again later.')
                    //                     ->danger()
                    //                     ->send();
                    //             }
                    //         }),
                    //     Action::make('downloadStatement')
                    //         ->label('Download')
                    //         ->icon('heroicon-m-document-arrow-down')
                    //         ->action(function ($record, $livewire) {
                    //             try {
                    //                 $url = URL::signedRoute('statement.download', ['student' => $record->id]);
                    //                 $livewire->js("window.location.href = '$url';");
                    //             } catch (\Exception $e) {
                    //                 Log::error('Statement download failed:', ['error' => $e->getMessage()]);
                    //                 Notification::make()
                    //                     ->title('Download Error')
                    //                     ->body('Unable to download statement. Please try again later.')
                    //                     ->danger()
                    //                     ->send();
                    //             }
                    //         }),
                    // ])
                    //     ->link()
                    //     ->icon(false)
                    //     ->label('Statement')
                    //     ->extraAttributes([
                    //         'style' => 'margin-left: 0.75rem !important; margin-bottom: 0.5rem !important;',
                    //     ]),

                ])->icon('heroicon-m-document-text')
                    ->tooltip('Export documents')
                    ->size(ActionSize::Large),
            ])

            ->bulkActions([
                BulkActionGroup::make([
                    BulkActionGroup::make([
                        BulkAction::make('printCompilation')
                            ->visible(fn () => main_staff_plus_h_o_d_access())
                            ->icon('heroicon-o-printer')
                            ->label('Print selected')
                            ->requiresConfirmation()
                            ->modalDescription(fn (Collection $records) => count($records) > 20 ? 'Printing '.count($records).' '.Str::plural('page', count($records)).' may take time to load.' : null)
                            ->modalHeading(fn (Collection $records) => 'Print '.count($records).' '.Str::plural('Compilation', count($records)).'?')
                            ->action(function (Collection $records, $livewire) {
                                try {
                                    $compilationsData = [];
                                    foreach ($records as $record) {
                                        $compilationsData[] = self::getCompiledData($livewire, $record);
                                    }

                                    $compilationCacheKey = 'compilationData_'.uniqid();
                                    Cache::put($compilationCacheKey, $compilationsData, now()->addMinutes(10));

                                    $url = URL::signedRoute('compilation.print', ['compilationCacheKey' => $compilationCacheKey]);

                                    $livewire->js("(function() {
                                                const newWindow = window.open(
                                                    '$url',
                                                    'Compilation',
                                                    'width=1024,height=768,toolbar=no,menubar=no,scrollbars=yes,resizable=yes'
                                                );

                                                if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                                                    alert('Pop-up blocked! Please allow pop-ups to print.');
                                                } else {
                                                    newWindow.focus();
                                                }
                                            })();");

                                    Notification::make()
                                        ->title('Print Window Opened')
                                        ->body('Opening '.count($records).' '.Str::plural('compilation', count($records)).' for printing.')
                                        ->success()
                                        ->send();

                                } catch (\Exception $e) {
                                    Log::error('Bulk compilation print failed:', ['error' => $e->getMessage()]);

                                    Notification::make()
                                        ->title('Print Error')
                                        ->body('Unable to print compilations. Please try again.')
                                        ->danger()
                                        ->send();
                                }
                            })
                            ->deselectRecordsAfterCompletion(),
                        BulkAction::make('downloadCompilation')
                            ->visible(fn () => main_staff_plus_h_o_d_access())
                            ->icon('heroicon-m-document-arrow-down')
                            ->label('Download selected')
                            ->requiresConfirmation()
                            ->modalHeading(fn (Collection $records) => 'Download '.count($records).' '.Str::plural('Compilation', count($records)).'?')
                            ->modalDescription(fn (Collection $records) => count($records) > 20
                                                                ? 'This will generate a '.count($records).'-page PDF in the background. You will receive a notification when it\'s ready (usually 2-5 minutes).'
                                                                : 'This will download a '.count($records).'-page PDF immediately.'
                            )
                            ->action(function (Collection $records, $livewire, $action) {
                                try {

                                    if ($records->count() > 100) {
                                        Notification::make()
                                            ->title('Download Limit Exceeded')
                                            ->body('You can only download up to 100 compilations at a time. Please reduce your selection and try again.')
                                            ->danger()
                                            ->send();

                                        $action->halt();
                                    }

                                    $compilationsData = [];
                                    foreach ($records as $record) {
                                        $compilationsData[] = self::getCompiledData($livewire, $record);
                                    }

                                    $compilationCacheKey = 'compilationData_'.uniqid();
                                    Cache::put($compilationCacheKey, $compilationsData, now()->addMinutes(20));

                                    $url = URL::signedRoute('compilation.download', ['compilationCacheKey' => $compilationCacheKey]);

                                    if (count($records) > 20) {
                                        // Background job - just redirect to trigger it
                                        $livewire->js("window.location.href = '$url';");

                                        Notification::make()
                                            ->title('Processing PDF')
                                            ->body('Generating '.count($records).'-page PDF. Check notifications for download link.')
                                            ->info()
                                            ->send();
                                    } else {
                                        // Direct download
                                        $livewire->js("window.location.href = '$url';");

                                        Notification::make()
                                            ->title('Download Started')
                                            ->body('Downloading '.count($records).' '.Str::plural('compilation', count($records)).'.')
                                            ->success()
                                            ->send();
                                    }

                                } catch (\Exception $e) {
                                    Log::error('Bulk compilation download failed:', ['error' => $e->getMessage()]);

                                    Notification::make()
                                        ->title('Download Failed')
                                        ->body('Unable to download compilations. Please try again.')
                                        ->danger()
                                        ->send();
                                }
                            })
                            ->deselectRecordsAfterCompletion(),

                    ])
                        ->link()
                        ->icon(false)
                        ->label('Compilation')
                        ->extraAttributes([
                            'style' => 'margin-left: 0.75rem !important; margin-bottom: 0.5rem !important;  margin-top: 0.5rem !important;',
                        ]),
                ]),
            ]);
    }

    private static function getResultData(HasTable $livewire, $record): array
    {
        $student = $record;

        $assessmentNames = Assessment::pluck('max_score', 'name')->all();
        $courseData = self::getSemesterCourseData($livewire, $student) ?? [];

        // Semester calculations
        $semesterTotalCreditUnit = self::getSemesterTotalCreditUnit($courseData);
        $semesterTotalGradePoint = self::getSemesterTotalGradePoint($courseData);
        $semesterGradePointAverage = $semesterTotalCreditUnit > 0 ? number_format($semesterTotalGradePoint / $semesterTotalCreditUnit, 2) : null;
        $semesterGradeRemark = self::getRemarkFromGradePointAverage($semesterGradePointAverage);
        $semesterOutstandingCourses = self::getSemesterOutstandingCourses($courseData);

        // Cumulative calculations
        $cumulativeTotalCreditUnit = self::getCumulativeTotalCreditUnit($livewire, $student);
        $cumulativeTotalGradePoint = self::getCumulativeTotalGradePoint($livewire, $student);
        $cumulativeGradePointAverage = $cumulativeTotalCreditUnit > 0 ? number_format($cumulativeTotalGradePoint / $cumulativeTotalCreditUnit, 2) : null;
        $cumulativeGradeRemark = self::getRemarkFromGradePointAverage($cumulativeGradePointAverage);
        $cumulativeOutstandingCourses = self::getCumulativeOutstandingCourses($livewire, $student);

        return [

            'student' => $student,
            'tableFilters' => self::extractFilters($livewire),

            'courseData' => $courseData,
            'assessmentNames' => $assessmentNames,

            'semesterTotalCreditUnit' => $semesterTotalCreditUnit,
            'semesterTotalGradePoint' => $semesterTotalGradePoint,
            'semesterGradePointAverage' => $semesterGradePointAverage,
            'semesterGradeRemark' => $semesterGradeRemark,
            'semesterOutstandingCourses' => $semesterOutstandingCourses,

            'cumulativeTotalCreditUnit' => $cumulativeTotalCreditUnit,
            'cumulativeTotalGradePoint' => $cumulativeTotalGradePoint,
            'cumulativeGradePointAverage' => $cumulativeGradePointAverage,
            'cumulativeGradeRemark' => $cumulativeGradeRemark,
            'cumulativeOutstandingCourses' => $cumulativeOutstandingCourses,
        ];
    }

    private static function getCompiledData(HasTable $livewire, $record): array
    {
        $student = $record->load('activeProgramme');
        $departmentId = self::extractFilters($livewire)['department_id'];

        $registrations = Registration::where('user_id', $student->id)
            ->with('level', 'semester')
            ->get();

        $compilationData = [];
        $grades = Grade::all();

        foreach ($registrations as $registration) {

            $isPublished = Scoresheet::where([
                'school_session_id' => $registration->school_session_id,
                'semester_id' => $registration->semester_id,
                'department_id' => $departmentId,
                'is_published' => true,
            ])->exists();

            if (! $isPublished) {
                continue;
            }

            $courses = Course::select('id', 'code', 'title', 'credit', 'course_status')
                ->where('semester_id', $registration->semester_id)
                ->where('level_id', $registration->level_id)
                ->where('department_id', $departmentId)
                ->orderBy('code')
                ->get();

            foreach ($courses as $course) {
                $totalScore = TotalScore::where([
                    'registration_id' => $registration->id,
                    'course_id' => $course->id,
                ])->value('total');

                $grade = self::getGradeFromScore($totalScore, $grades);
                $gradePoint = ($grade ? $grade->point * $course->credit : null);

                $compilationData[] = [
                    'code' => $course->code,
                    'title' => $course->title,
                    'credit' => $course->credit,
                    'status' => $course->course_status,
                    'total_score' => $totalScore,
                    'grade' => $grade?->name,
                    'point' => $grade?->point,
                    'grade_point' => $gradePoint,
                    'level' => $registration->level->name,
                    'semester' => $registration->semester->name,
                ];
            }
        }

        $cumulativeTotalCreditUnit = self::getCumulativeTotalCreditUnit($livewire, $student);
        $cumulativeTotalGradePoint = self::getCumulativeTotalGradePoint($livewire, $student);
        $cumulativeGradePointAverage = $cumulativeTotalCreditUnit > 0 ?
            number_format($cumulativeTotalGradePoint / $cumulativeTotalCreditUnit, 2) : null;
        $cumulativeGradeRemark = self::getRemarkFromGradePointAverage($cumulativeGradePointAverage);

        $dept = Department::find($departmentId);

        return [
            'student' => $student,
            'compilationData' => $compilationData,
            'cumulativeTotalCreditUnit' => $cumulativeTotalCreditUnit,
            'cumulativeTotalGradePoint' => $cumulativeTotalGradePoint,
            'cumulativeGradePointAverage' => $cumulativeGradePointAverage,
            'cumulativeGradeRemark' => $cumulativeGradeRemark,
            'dept' => $dept,
        ];
    }

    public static function getCourses(HasTable $livewire)
    {
        $filters = self::extractFilters($livewire);

        return Course::select('id', 'code', 'title', 'credit', 'course_status')
            ->where('department_id', $filters['department_id'])
            ->where('level_id', $filters['level_id'])
            ->where('semester_id', $filters['semester_id'])
            ->get();
    }

    public static function getGradeFromScore(?int $totalScore, ?Collection $grades = null)
    {
        if ($totalScore === null) {
            return null;
        }

        static $grades;
        $grades ??= Grade::all();

        return $grades->first(
            fn ($grade) => $totalScore >= $grade->min_score && $totalScore <= $grade->max_score
        );
    }

    public static function getRemarkFromGradePointAverage(?float $gradePointAverage, ?Collection $grades = null)
    {
        if ($gradePointAverage === null) {
            return null;
        }

        static $grades;
        $grades ??= Grade::all();

        return $grades->first(
            fn ($grade) => $gradePointAverage >= $grade->min_point && $gradePointAverage <= $grade->max_point
        );
    }

    private static function getRegistration(int $userId, array $filters): ?Registration
    {
        return Registration::where([
            'user_id' => $userId,
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'level_id' => $filters['level_id'],
        ])->first();
    }

    public static function getFailedScore(): int
    {
        static $failedScore;
        $failedScore ??= Grade::where('min_score', 0)->value('max_score');

        return $failedScore;
    }

    public static function getCourseData(HasTable $livewire, $record, $course)
    {
        $filters = self::extractFilters($livewire);

        return self::getCourseDataWithFilters($filters, $record, $course);
    }

    public static function getCourseDataWithFilters(array $filters, $record, $course)
    {
        $registration = self::getRegistration($record->id, $filters);

        if (! $registration) {
            return null;
        }

        $totalScore = TotalScore::where([
            'registration_id' => $registration->id,
            'course_id' => $course->id,
        ])->value('total');

        $grade = self::getGradeFromScore($totalScore);

        return [
            'total_score' => $totalScore,
            'grade' => $grade?->name,
            'point' => $grade?->point,
            'grade_point' => $grade ? $grade->point * $course->credit : null,
        ];
    }

    public static function getSemesterTotalCreditUnit($courses)
    {
        if (is_array($courses)) {
            return collect($courses)->sum('credit');
        }

        return $courses->sum('credit');
    }

    public static function getSemesterCourseData(HasTable $livewire, User $student): array
    {
        $filters = self::extractFilters($livewire);
        if (! self::hasRequiredFilters($livewire) || ! self::isScoresheetPublished($livewire)) {
            return [];
        }

        $registration = self::getRegistration($student->id, $filters);
        if (! $registration) {
            return [];
        }

        // Fetch courses for the selected filters
        $courses = Course::select('id', 'code', 'title', 'credit', 'course_status')
            ->where('semester_id', $filters['semester_id'])
            ->where('level_id', $filters['level_id'])
            ->where('department_id', $filters['department_id'])
            ->get();

        $grades = Grade::all();

        return $courses->map(function ($course) use ($registration, $grades) {
            $scores = Score::with('assessment')
                ->where('course_id', $course->id)
                ->where('registration_id', $registration->id)
                ->get();

            $totalScore = TotalScore::where('course_id', $course->id)
                ->where('registration_id', $registration->id)
                ->value('total');

            $grade = self::getGradeFromScore($totalScore, $grades);
            $assessmentScores = $scores->mapWithKeys(fn ($score) => [
                $score->assessment->name => $score->score,
            ])->all();

            return [
                'code' => $course->code,
                'status' => $course->course_status,
                'title' => $course->title,
                'credit' => $course->credit,
                ...$assessmentScores,
                'total_score' => $totalScore,
                'grade' => $grade?->name,
                'point' => $grade?->point,
                'grade_point' => ($grade ? $grade->point * $course->credit : null),
            ];
        })->toArray();
    }

    public static function getSemesterTotalGradePoint($courseData): ?float
    {
        if (empty($courseData)) {
            return null;
        }

        return collect($courseData)->sum('grade_point') ?? null;
    }

    public static function getSemesterOutstandingCourses($courseData): \Illuminate\Support\Collection
    {
        return collect($courseData)->filter(fn ($course) => ($course['total_score'] ?? -1) <= self::getFailedScore());
    }

    public static function getSemesterGradePoint(Registration $registration, Course $course, Collection $grades): ?float
    {
        $totalScore = TotalScore::where([
            'registration_id' => $registration->id,
            'course_id' => $course->id,
        ])->value('total');

        $grade = self::getGradeFromScore($totalScore, $grades);

        return $grade ? (float) ($grade->point * $course->credit) : null;
    }

    public static function getSemesterTotalGradePointWithFilters(array $filters, $record, $courses)
    {
        return $courses->map(fn ($course) => self::getCourseDataWithFilters($filters, $record, $course)['grade_point'])->sum();
    }

    public static function getSemesterOutstandingCoursesWithFilters(array $filters, $record, $courses): \Illuminate\Support\Collection
    {
        return $courses->filter(function ($course) use ($filters, $record) {
            $data = self::getCourseDataWithFilters($filters, $record, $course);

            return $data && ($data['total_score'] ?? -1) <= self::getFailedScore();
        });
    }

    public static function getCumulativeTotalCreditUnit(HasTable $livewire, User $student): int
    {
        $filters = self::extractFilters($livewire);

        return self::getCumulativeTotalCreditUnitWithFilters($filters, $student);
    }

    public static function getCumulativeTotalCreditUnitWithFilters(array $filters, User $student): int
    {
        $registrations = $student->registrations;

        // Collect all registered combinations of level, semester, and session
        $registeredCombos = $registrations->map(function ($reg) {
            return [
                'level_id' => $reg->level_id,
                'semester_id' => $reg->semester_id,
                'school_session_id' => $reg->school_session_id,
            ];
        });

        // Filter only those combinations where result is published
        $publishedCombos = $registeredCombos->filter(function ($combo) use ($filters) {
            return Scoresheet::where([
                'semester_id' => $combo['semester_id'],
                'school_session_id' => $combo['school_session_id'],
                'department_id' => $filters['department_id'],
                'is_published' => true,
            ])->exists();
        })->values();

        if ($publishedCombos->isEmpty()) {
            return 0;
        }

        // Fetch courses only for those published level + semester combos
        $courses = Course::select('credit')
            ->where('department_id', $filters['department_id'])
            ->where(function ($query) use ($publishedCombos) {
                foreach ($publishedCombos as $combo) {
                    $query->orWhere(function ($sub) use ($combo) {
                        $sub->where('level_id', $combo['level_id'])
                            ->where('semester_id', $combo['semester_id']);
                    });
                }
            })
            ->get();

        return $courses->sum('credit');
    }

    public static function getCumulativeTotalGradePoint(HasTable $livewire, User $student): float
    {
        $filters = self::extractFilters($livewire);

        return self::getCumulativeTotalGradePointWithFilters($filters, $student);
    }

    public static function getCumulativeTotalGradePointWithFilters(array $filters, User $student): float
    {
        $registrations = $student->registrations;
        $totalGradePoint = 0.0;

        // Collect all registered combinations of level, semester, and session
        $registeredCombos = $registrations->map(function ($reg) {
            return [
                'level_id' => $reg->level_id,
                'semester_id' => $reg->semester_id,
                'school_session_id' => $reg->school_session_id,
            ];
        });

        // Filter only those combinations where result is published
        $publishedCombos = $registeredCombos->filter(function ($combo) use ($filters) {
            return Scoresheet::where([
                'semester_id' => $combo['semester_id'],
                'school_session_id' => $combo['school_session_id'],
                'department_id' => $filters['department_id'],
                'is_published' => true,
            ])->exists();
        })->values();

        if ($publishedCombos->isEmpty()) {
            return 0.0;
        }

        // Fetch courses only for those published level + semester combos
        $courses = Course::select('id', 'credit', 'level_id', 'semester_id')
            ->where('department_id', $filters['department_id'])
            ->where(function ($query) use ($publishedCombos) {
                foreach ($publishedCombos as $combo) {
                    $query->orWhere(function ($sub) use ($combo) {
                        $sub->where('level_id', $combo['level_id'])
                            ->where('semester_id', $combo['semester_id']);
                    });
                }
            })
            ->get();

        // Calculate grade points for each course
        foreach ($courses as $course) {
            $combo = $publishedCombos->first(function ($item) use ($course) {
                return $item['level_id'] == $course->level_id && $item['semester_id'] == $course->semester_id;
            });

            if (! $combo) {
                continue;
            }

            $registration = $student->registrations->first(function ($reg) use ($combo) {
                return $reg->level_id == $combo['level_id'] &&
                    $reg->semester_id == $combo['semester_id'] &&
                    $reg->school_session_id == $combo['school_session_id'];
            });

            if (! $registration) {
                continue;
            }

            $totalScore = TotalScore::where([
                'registration_id' => $registration->id,
                'course_id' => $course->id,
            ])->value('total');

            if ($totalScore !== null) {
                $grade = self::getGradeFromScore($totalScore);
                if ($grade) {
                    $totalGradePoint += $grade->point * $course->credit;
                }
            }
        }

        return $totalGradePoint;
    }

    public static function getCumulativeOutstandingCourses(HasTable $livewire, User $record): \Illuminate\Support\Collection
    {
        $filters = self::extractFilters($livewire);

        return self::getCumulativeOutstandingCoursesWithFilters($record, $filters);
    }

    public static function getCumulativeOutstandingCoursesWithFilters(User $record, array $filters)
    {
        $failedScore = self::getFailedScore();
        $registrations = $record->registrations;

        // Collect all registered combinations of level, semester, and session
        $registeredCombos = $registrations->map(function ($reg) {
            return [
                'level_id' => $reg->level_id,
                'semester_id' => $reg->semester_id,
                'school_session_id' => $reg->school_session_id,
            ];
        });

        // Filter only those combinations where result is published
        $publishedCombos = $registeredCombos->filter(function ($combo) use ($filters) {
            return Scoresheet::where([
                'semester_id' => $combo['semester_id'],
                'school_session_id' => $combo['school_session_id'],
                'department_id' => $filters['department_id'],
                'is_published' => true,
            ])->exists();
        })->values();

        if ($publishedCombos->isEmpty()) {
            return collect();
        }

        // Fetch courses only for those published level + semester combos
        $courses = Course::select('id', 'code', 'level_id', 'semester_id')
            ->where('department_id', $filters['department_id'])
            ->where(function ($query) use ($publishedCombos) {
                foreach ($publishedCombos as $combo) {
                    $query->orWhere(function ($sub) use ($combo) {
                        $sub->where('level_id', $combo['level_id'])
                            ->where('semester_id', $combo['semester_id']);
                    });
                }
            })
            ->get();

        // Filter failed courses based on registration and published combo
        $failedCourses = $courses->filter(function ($course) use ($record, $failedScore, $publishedCombos) {
            $combo = $publishedCombos->first(function ($item) use ($course) {
                return $item['level_id'] == $course->level_id && $item['semester_id'] == $course->semester_id;
            });

            if (! $combo) {
                return false;
            }

            $registration = $record->registrations->first(function ($reg) use ($combo) {
                return $reg->level_id == $combo['level_id'] &&
                    $reg->semester_id == $combo['semester_id'] &&
                    $reg->school_session_id == $combo['school_session_id'];
            });

            if (! $registration) {
                return false;
            }

            $totalScore = TotalScore::where([
                'registration_id' => $registration->id,
                'course_id' => $course->id,
            ])->value('total');

            return $totalScore <= $failedScore;
        });

        return $failedCourses;
    }

    private static function isScoresheetAvailable(HasTable $livewire): bool
    {
        return self::isScoresheetCreated($livewire) && self::isScoresheetPublished($livewire);
    }

    private static function isCourseAvailable(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return Course::where([
            'level_id' => $filters['level_id'],
            'semester_id' => $filters['semester_id'],
            'department_id' => $filters['department_id'],
        ])->exists();
    }

    public static function isScoresheetCreated(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return Scoresheet::where([
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'department_id' => $filters['department_id'],
        ])->exists();
    }

    public static function isScoresheetPublished(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return Scoresheet::where([
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'department_id' => $filters['department_id'],
            'is_published' => true,
        ])->exists();
    }

    private static function getEmptyStateHeading(HasTable $livewire): string
    {
        if (! self::hasRequiredFilters($livewire)) {
            return 'All Options Must Be Selected to View Overview';
        }

        if (! self::isScoresheetCreated($livewire)) {
            return 'Scoresheet Has Not Been Created Yet';
        }

        if (! self::isScoresheetPublished($livewire)) {
            return 'Scoresheet Has Not Been Published Yet';
        }

        if (! self::isCourseAvailable($livewire)) {
            return 'No Courses Found';
        }

        return 'No Overview Found';
    }

    private static function getEmptyStateDescription(HasTable $livewire): string
    {
        if (! self::hasRequiredFilters($livewire)) {
            return 'Select <b>session</b>, <b>semester</b>, <b>level</b>, and <b>department</b> to view the overview.';
        }

        if (! self::isScoresheetCreated($livewire)) {
            return 'The scoresheet for this session, semester, and department has not been created yet. <b>Contact your school admin</b> for more information.';
        }

        if (! self::isScoresheetPublished($livewire)) {
            return 'The scoresheet for this session, semester, and department has not been published yet. <b>Contact your school admin</b> for more information.';
        }

        if (! self::isCourseAvailable($livewire)) {
            return 'No courses found for this session, semester, level, and department.';
        }

        return 'No overview found for the selected options.';
    }

    public static function extractFilters(HasTable $livewire): array
    {
        return $livewire->tableFilters['result_filter'] ?? [
            'school_session_id' => null,
            'semester_id' => null,
            'level_id' => null,
            'department_id' => null,
        ];
    }

    private static function hasRequiredFilters($livewire): bool
    {
        return ! in_array(null, self::extractFilters($livewire), true);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageOverview::route('/'),
        ];
    }
}
