<?php

namespace App\Http\Controllers;

use App\Models\Department;
use App\Models\Level;
use App\Models\SchoolSession;
use App\Models\Semester;
use App\Settings\CollegeSettings;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Browsershot\Browsershot;
use <PERSON><PERSON>\LaravelPdf\Facades\Pdf;

class OverviewController extends Controller
{
    public function print($overviewCacheKey)
    {
        return view('filament.documents.overview', $this->getOverviewData($overviewCacheKey));
    }

    public function download($overviewCacheKey)
    {
        $overviewData = $this->getOverviewData($overviewCacheKey);

        return Pdf::view('filament.documents.overview', $overviewData)
            ->withBrowsershot(fn (Browsershot $browsershot) => $browsershot->noSandbox())
            ->name($overviewData['fileName'].'.pdf')
            ->download();
    }

    private function getOverviewData($overviewCacheKey)
    {
        $overviewData = Cache::get($overviewCacheKey);

        if (! $overviewData) {
            abort(419, 'Page expired. Please regenerate the overview.');
        }

        $filters = $overviewData['filters'];

        $session = isset($filters['school_session_id'])
            ? SchoolSession::find($filters['school_session_id'])?->name
            : null;

        $semester = isset($filters['semester_id'])
            ? Semester::find($filters['semester_id'])?->name
            : null;

        $level = isset($filters['level_id'])
            ? Level::find($filters['level_id'])?->name
            : null;

        $department = isset($filters['department_id'])
            ? Department::find($filters['department_id'])?->name
            : null;

        $fileName = 'Overview - '.Str::slug(Str::replace('/', '-', implode(' ', [
            $session,
            $semester,
            $level,
            $department,
        ])));

        $collegeSettings = app(CollegeSettings::class);

        return (array) $overviewData + [
            'livewire' => null, // Set to null for document views since Livewire component can't be serialized
            'session' => $session,
            'semester' => $semester,
            'level' => $level,
            'department' => $department,
            'fileName' => $fileName,
            'collegeSettings' => $collegeSettings,
        ];
    }
}
