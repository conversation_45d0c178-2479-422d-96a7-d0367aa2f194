{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "bezhansalleh/filament-google-analytics": "*", "devonab/filament-easy-footer": "^1.1", "filament/filament": "3.3", "filament/spatie-laravel-settings-plugin": "3.3", "guava/filament-knowledge-base": "^1.14", "laravel/framework": "^12.0", "laravel/pulse": "^1.4", "laravel/reverb": "^1.5", "laravel/tinker": "^2.10.1", "marcogermani87/filament-captcha": "^1.4", "nyholm/psr7": "^1.8", "opcodesio/log-viewer": "^3.17", "railsware/mailtrap-php": "^3.1", "ralphjsmit/laravel-seo": "^1.7", "setasign/fpdf": "^1.8", "setasign/fpdi": "^2.6", "spatie/laravel-pdf": "^1.5", "spatie/laravel-sitemap": "^7.3", "symfony/http-client": "^7.3", "symfony/mailer": "^7.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "fakerphp/faker": "^1.23", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.8", "pestphp/pest-plugin-laravel": "^3.2", "spatie/laravel-ignition": "^2.9"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"npm run dev\" --names='server,queue,vite'"], "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}