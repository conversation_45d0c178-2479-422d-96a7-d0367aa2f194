@php
use App\Filament\Staff\Resources\OverviewResource;
@endphp

<div class="my-3 space-y-3">
    <!-- Result Compilation -->
    <div class="border border-gray-300 rounded-sm overflow-hidden">
        <div class="text-center px-3 py-1">
            <h2 class="font-bold mb-2">{{ $dept->name }} Result Compilation</h2>

            @if(count($compilationData) > 0)
            @php
            // Group courses by level for display
            $coursesByLevel = collect($compilationData)->groupBy('level');
            @endphp

            <!-- Level Headers and Course Data -->
            <div class="flex flex-wrap md:flex-nowrap gap-4 justify-center">
                @foreach($coursesByLevel as $levelName => $levelCourses)
                <div class="w-full md:w-auto flex-1 border-gray-300">
                    <!-- Level Header -->
                    <div class="bg-gray-100 px-2 text-center border">
                        <h3 class="font-bold text-base">{{ $levelName }}</h3>
                    </div>

                    <!-- Course Data Table -->
                    <table class="w-full table-auto text-xs border-collapse">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="border px-1 py-1">#</th>
                                <th class="border px-1 py-1">Course</th>
                                <th class="border px-1 py-1" x-tooltip.raw="Credit Unit">CU</th>
                                <th class="border px-1 py-1" x-tooltip.raw="Total Score">S</th>
                                <th class="border px-1 py-1" x-tooltip.raw="Grade">G</th>
                                <th class="border px-1 py-1" x-tooltip.raw="Point">P</th>
                                <th class="border px-1 py-1" x-tooltip.raw="Grade Point">GP</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($levelCourses as $index => $course)
                            @php
                            $failed = ($course['total_score'] ?? -1) <= OverviewResource::getFailedScore(); @endphp <tr>
                                <td class="border px-1 py-1 text-center">{{ $index + 1 }}</td>
                                <td class="border px-1 py-1" x-tooltip.raw="{{ $course['title'] }}">
                                    {{ $course['code'] }} <span class="text-gray-800"> {{ $course['status'] }}</span>
                                </td>
                                <td class="border px-1 py-1 text-center">{{ $course['credit'] }}</td>
                                <td class="border px-1 py-1 text-center {{ $failed ? 'text-red-600' : '' }}">
                                    {{ $course['total_score'] ?? '-' }}
                                </td>
                                <td class="border px-1 py-1 text-center {{ $failed ? 'text-red-600' : '' }}">
                                    {{ $course['grade'] ?? '-' }}
                                </td>
                                <td class="border px-1 py-1 text-center">{{ $course['point'] ?? '-' }}</td>
                                <td class="border px-1 py-1 text-center">{{ $course['grade_point'] ?? '-' }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="border px-1 py-2 text-center text-gray-500">
                                        No courses found
                                    </td>
                                </tr>
                                @endforelse
                        </tbody>
                    </table>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <div class="flex flex-col md:flex-row gap-4 flex-col-custom">
        <!-- Compilation Summary -->
        <div class="flex-1 border border-gray-300 rounded-sm px-3 py-1">
            <h3 class="font-bold text-center mb-2">Compilation Summary</h3>
            <div>
                <table class="w-full table-auto text-sm">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="border px-2 py-1" x-tooltip.raw="Cumulative Total Credit Unit">CTCU</th>
                            <th class="border px-2 py-1" x-tooltip.raw="Cumulative Total Grade Point">CTGP</th>
                            <th class="border px-2 py-1" x-tooltip.raw="Cumulative Grade Point Average">CGPA</th>
                            <th class="border px-2 py-1" x-tooltip.raw="Cumulative Remark">C.Remark</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="border px-2 py-1 text-center">
                                {{ $cumulativeTotalCreditUnit }}
                            </td>
                            <td class="border px-2 py-1 text-center">
                                {{ $cumulativeTotalGradePoint }}
                            </td>
                            <td class="border px-2 py-1 text-center">
                                {{ $cumulativeGradePointAverage ?? '-' }}
                            </td>
                            <td class="border px-2 py-1 text-center">
                                {{ $cumulativeGradeRemark?->remark ?? '-' }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- H.O.D's Endorsement -->
        @if($isExport)
        <div class="flex-1 border border-gray-300 rounded-sm px-3 py-1">
            <h3 class="font-bold text-center mb-2">H.O.D's Endorsement</h3>
            <div class="space-y-4">
                <div class="flex items-end">
                    <span class="font-medium">Name:</span>
                    @if ($dept->headOfDepartments)
                    @php $hod = $dept->headOfDepartments->first(); @endphp
                    <span class="ml-2">{{ $hod->title?->getLabel() }} {{ $hod->name }}</span>
                    @else
                    <div class="border-b border-gray-400 flex-1 ml-2"></div>
                    @endif
                </div>

                <div class="flex items-end">
                    <span class="font-medium">Sign & Date:</span>
                    <div class="border-b border-gray-400 flex-1 ml-2"></div>
                </div>
            </div>
        </div>
        @endif

    </div>

    @if($isExport)
    <!-- Key -->
    <div class="border border-gray-300 rounded-sm overflow-hidden px-3 py-1">
        <h2 class="font-bold text-center mb-2">Key</h2>
        <div class="text-sm text-center">
            <strong>CU</strong> – Credit Unit | <strong>S</strong> – Score | <strong>G</strong> – Grade |
            <strong>P</strong> – Point | <strong>GP</strong> – Grade Point | <strong>CTCU</strong> – Cumulative Total
            Credit Unit | <strong>CTGP</strong> – Cumulative Total Grade Point | <strong>CGPA</strong> – Cumulative
            Grade Point Average | <strong>C.Remark</strong> – Cumulative Remark
        </div>
    </div>
    @endif

    @else
    <div class="text-center py-8 text-gray-500">
        <p class="text-lg">No compilation data available</p>
        <p class="text-sm">No published results found for any level.</p>
    </div>
    @endif
</div>