<?php

namespace App\Services;

use App\Enums\AdmissionStatus;
use App\Enums\FeeType;
use App\Enums\InvoiceStatus;
use App\Enums\PaymentMethod;
use App\Enums\Role;
use App\Enums\ScreeningStatus;
use App\Enums\TransactionStatus;
use App\Enums\TransactionType;
use App\Models\Invoice;
use App\Models\Registration;
use App\Models\SchoolSession;
use App\Models\Semester;
use App\Models\Transaction;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class EventService
{
    public function handleChargeSuccess(array $payload)
    {
        $reference = $payload['data']['reference'];
        $amountInKobo = $payload['data']['amount'];
        $channel = $payload['data']['channel'];
        $amountInNaira = MoneyService::toNaira($amountInKobo);
        $invoice = Invoice::withTrashed()->where('reference', $reference)->first();

        if (! $invoice) {
            Log::error('No invoice found for reference', ['reference' => $reference]);

            return;
        }

        if ($invoice->invoice_status === InvoiceStatus::PAID) {
            Log::info('Invoice already paid', ['reference' => $reference]);

            return;
        }

        if ($invoice->total_amount !== $amountInNaira) {
            Log::error('Amount mismatch', [
                'reference' => $reference,
                'expected' => $invoice->total_amount,
                'received' => $amountInNaira,
            ]);

            return;
        }

        DB::transaction(function () use ($invoice, $channel) {
            $invoice->update([
                'invoice_status' => InvoiceStatus::PAID,
                'paid_at' => now(),
            ]);

            if ($invoice->trashed()) {
                $invoice->restore();
            }

            if ($invoice->fee_type === FeeType::PORTAL && $invoice->user_id === null) {
                $applicationData = $invoice->metadata['applicationData'] ?? [];

                if (empty($applicationData)) {
                    Log::error('Application data missing in metadata', [
                        'reference' => $invoice->reference,
                        'metadata' => $invoice->metadata,
                    ]);
                    throw new \Exception('Missing application data');
                }

                $processed = $this->processFormData($applicationData);

                $user = User::create($processed['user']);

                Transaction::create([
                    'invoice_id' => $invoice->id,
                    'number' => app(CodeGenerationService::class)->generateTransactionNumber(),
                    'amount' => $invoice->total_amount,
                    'transaction_status' => TransactionStatus::SUCCESS,
                    'transaction_type' => TransactionType::CREDIT,
                    'payment_method' => PaymentMethod::ONLINE,
                    'payment_channel' => $channel,
                ]);

                $user->application()->create($processed['application']);
                $user->registrations()->create($processed['registration']);
                $user->guardian()->create($processed['guardian']);

                $invoice->update([
                    'user_id' => $user->id,
                    // 'payable_id' => $user->registrations()->first()->id,
                    // 'payable_type' => Registration::class,
                ]);

                $semester = Semester::find($processed['registration']['semester_id']);
                $session = SchoolSession::find($processed['registration']['school_session_id']);

                User::whereIn('role', management_staff_roles())->get()->each(function ($recipient) use ($user, $semester, $session) {
                    try {
                        Notification::make()
                            ->title('Application Received')
                            ->icon('heroicon-o-check-circle')
                            ->iconColor('success')
                            ->body(new HtmlString("New application from <b>{$user->name}</b> for <b>{$semester->name}</b>, <b>{$session->name}</b>."))
                            ->sendToDatabase($recipient);
                    } catch (\Exception $e) {
                        Log::error('Notification failed', ['error' => $e->getMessage()]);
                    }
                });
            }

            if ($invoice->fee_type === FeeType::PORTAL && $invoice->user_id !== null) {
                Transaction::create([
                    'invoice_id' => $invoice->id,
                    'number' => app(CodeGenerationService::class)->generateTransactionNumber(),
                    'amount' => $invoice->total_amount,
                    'transaction_status' => TransactionStatus::SUCCESS,
                    'transaction_type' => TransactionType::CREDIT,
                    'payment_method' => PaymentMethod::ONLINE,
                    'payment_channel' => $channel,
                ]);
            }

            // Add more if blocks for future types
        });
    }

    protected function processFormData($applicationData): array
    {
        // Process user data
        $processedUserData = [
            'last_name' => $applicationData['last_name'],
            'first_name' => $applicationData['first_name'],
            'middle_name' => $applicationData['middle_name'],
            'email' => $applicationData['email'],
            'password' => $applicationData['password'],
            'role' => Role::STUDENT,
            'phone' => $applicationData['phone'],
            'photo' => $applicationData['photo'],
            'gender' => $applicationData['gender'],
            'marital_status' => $applicationData['marital_status'],
            'religion' => $applicationData['religion'],
            'nationality' => $applicationData['nationality'],
            'date_of_birth' => $applicationData['date_of_birth'],
            'address_line' => $applicationData['address_line'],
            'address_town' => $applicationData['address_town'],
            'address_state' => $applicationData['address_state'],
            'state_id' => $applicationData['state_id'],
            'local_government_area_id' => $applicationData['local_government_area_id'],
        ];

        // Process guardian data
        $processedGuardianData = [
            'title' => $applicationData['guardian']['title'],
            'last_name' => $applicationData['guardian']['last_name'],
            'first_name' => $applicationData['guardian']['first_name'],
            'relationship' => $applicationData['guardian']['relationship'],
            'occupation' => $applicationData['guardian']['occupation'],
            'phone' => $applicationData['guardian']['phone'],
        ];

        // Process application data
        $processedApplicationData = [
            'number' => (new CodeGenerationService)->generateApplicationNumber(),
            'school_session_id' => $applicationData['application']['school_session_id'],
            'programme_id' => $applicationData['application']['programme_id'],
            'secondary_school_attended' => $applicationData['application']['secondary_school_attended'],
            'secondary_school_graduation_year' => $applicationData['application']['secondary_school_graduation_year'],
            'jamb_registration_number' => $applicationData['application']['jamb_registration_number'],
            'jamb_score' => $applicationData['application']['jamb_score'] ?? null,
            'exam_board' => $applicationData['application']['exam_board'],
            'exam_result' => $applicationData['application']['exam_result'] ?? null,
            'admission_status' => AdmissionStatus::PENDING,
            'screening_status' => ScreeningStatus::PENDING,
            'is_declared' => true,
        ];

        // Process registration data
        $processedRegistrationData = [
            'school_session_id' => $applicationData['application']['school_session_id'],
            'semester_id' => activeSemester()->id ?? Semester::first()->id,
            // 'level_id' => $applicationData['application']['level_id'],
            'programme_id' => $applicationData['application']['programme_id'],
            'is_active' => false,
        ];

        return [
            'user' => $processedUserData,
            'guardian' => $processedGuardianData,
            'application' => $processedApplicationData,
            'registration' => $processedRegistrationData,
        ];
    }
}
