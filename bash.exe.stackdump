Stack trace:
Frame         Function      Args
0007FFFF35D0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF35D0, 0007FFFF24D0) msys-2.0.dll+0x2118E
0007FFFF35D0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF35D0  0002100469F2 (00021028DF99, 0007FFFF3488, 0007FFFF35D0, 000000000000) msys-2.0.dll+0x69F2
0007FFFF35D0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF35D0  00021006A545 (0007FFFF35E0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF35E0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC31810000 ntdll.dll
7FFC30720000 KERNEL32.DLL
7FFC2F430000 KERNELBASE.dll
7FFC2C730000 apphelp.dll
7FFC31630000 USER32.dll
7FFC2EFC0000 win32u.dll
7FFC310A0000 GDI32.dll
7FFC2F090000 gdi32full.dll
7FFC2EF20000 msvcp_win.dll
7FFC2F1A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC30660000 advapi32.dll
7FFC31120000 msvcrt.dll
7FFC2FA20000 sechost.dll
7FFC2FC60000 RPCRT4.dll
7FFC2E7F0000 CRYPTBASE.DLL
7FFC2F350000 bcryptPrimitives.dll
7FFC310F0000 IMM32.DLL
