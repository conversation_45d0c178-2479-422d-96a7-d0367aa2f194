<?php

namespace App\Filament\Student\Resources\RegistrationResource\Pages;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Student\Resources\RegistrationResource;

class ManageRegistrations extends ManageRecords
{
    protected static string $resource = RegistrationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

      public function printRegistration($registrationId)
    {
        try {
            $url = URL::signedRoute('registration.print', ['registration' => $registrationId]);

            // If this is a Livewire component
            return $this->js("(function() {
                const newWindow = window.open(
                    '$url',
                    'Registration',
                    'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                );
            
                if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                    alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                } else {
                    newWindow.focus();
                }
            })();");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Registration print failed:', ['error' => $e->getMessage()]);
            Notification::make()
                ->title('Print Failed')
                ->body('Unable to print Registration. Please try again later.')
                ->danger()
                ->send();
        }
    }

    public function downloadRegistration($registrationId)
    {
        try {
            $url = URL::signedRoute('registration.download', ['registration' => $registrationId]);

            // If this is a Livewire component
            return $this->js("window.location.href = '$url';");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Log::error('Registration download failed:', ['error' => $e->getMessage()]);
            Notification::make()
                ->title('Download Failed')
                ->body('Unable to download Registration. Please try again later.')
                ->danger()
                ->send();
        }
    }

    
}
